import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages'
import { BaseAgent } from './base/BaseAgent'
import { logger } from '../utils/logger'
import type {
  WorkflowState,
  AgentConfig,
  FinancialAnalysisResult,
  AssetAllocation,
  CashFlowAnalysis,
} from '../types/agents'
import {
  getUserAssets,
  getUserDebts,
  getUserProfile,
  calculateTotalAssets,
  calculateTotalDebts,
  calculateNetWorth,
  analyzeAssetAllocation,
  calculateFinancialHealthScore,
} from './tools'

export class FinancialAnalysisAgent extends BaseAgent {
  constructor(modelId: string = 'deepseek-chat') {
    const config: AgentConfig = {
      name: 'FinancialAnalysisAgent',
      description: '专业的财务分析代理，负责分析用户的资产负债情况，提供财务健康度评估',
      systemPrompt: `你是一位专业的财务分析师，具有丰富的个人财务规划经验。你的任务是：

1. 全面分析用户的资产和负债情况
2. 计算关键财务指标（净资产、资产配置比例、债务比率等）
3. 评估用户的财务健康状况
4. 识别财务风险和机会
5. 提供改善财务状况的具体建议

分析时请考虑：
- 资产的流动性和风险特征
- 债务的利率和还款压力
- 收支平衡和现金流状况
- 应急资金的充足性
- 资产配置的合理性

请以专业、客观的态度进行分析，提供具体可行的建议。`,
      tools: [
        'getUserAssets',
        'getUserDebts',
        'getUserProfile',
        'calculateTotalAssets',
        'calculateTotalDebts',
        'calculateNetWorth',
        'analyzeAssetAllocation',
        'calculateFinancialHealthScore',
      ],
      maxIterations: 3,
      temperature: 0.3,
      modelId,
    }

    super(config)
  }

  protected initializeTools(): void {
    this.tools.set('getUserAssets', getUserAssets)
    this.tools.set('getUserDebts', getUserDebts)
    this.tools.set('getUserProfile', getUserProfile)
    this.tools.set('calculateTotalAssets', calculateTotalAssets)
    this.tools.set('calculateTotalDebts', calculateTotalDebts)
    this.tools.set('calculateNetWorth', calculateNetWorth)
    this.tools.set('analyzeAssetAllocation', analyzeAssetAllocation)
    this.tools.set('calculateFinancialHealthScore', calculateFinancialHealthScore)
  }

  protected async processTask(
    state: WorkflowState,
    messages: BaseMessage[]
  ): Promise<FinancialAnalysisResult> {
    try {
      logger.info('开始财务分析')

      // 1. 获取用户数据
      const assetsResult = await this.callTool('getUserAssets', state.userId)
      const debtsResult = await this.callTool('getUserDebts', state.userId)
      const profileResult = await this.callTool('getUserProfile', state.userId)

      if (!assetsResult.success || !debtsResult.success) {
        throw new Error('获取用户财务数据失败')
      }

      const assets = assetsResult.output?.data || []
      const debts = debtsResult.output?.data || []
      const profile = profileResult.output?.data

      // 2. 计算基础财务指标
      const totalAssetsResult = await this.callTool('calculateTotalAssets', assets)
      const totalDebtsResult = await this.callTool('calculateTotalDebts', debts)

      if (!totalAssetsResult.success || !totalDebtsResult.success) {
        throw new Error('计算财务指标失败')
      }

      const totalAssets = totalAssetsResult.output?.data || 0
      const totalDebts = totalDebtsResult.output?.data || 0

      const netWorthResult = await this.callTool('calculateNetWorth', {
        totalAssets,
        totalDebts,
      })
      const netWorth = netWorthResult.output?.data || 0

      // 3. 分析资产配置
      const allocationResult = await this.callTool('analyzeAssetAllocation', assets)
      const assetAllocation: AssetAllocation[] = allocationResult.success
        ? allocationResult.output?.data || []
        : []

      // 4. 计算财务健康评分
      const monthlyIncome = profile?.monthlyIncome || 0
      const healthScoreResult = await this.callTool('calculateFinancialHealthScore', {
        netWorth,
        totalAssets,
        totalDebts,
        monthlyIncome,
      })
      const financialHealthScore = healthScoreResult.output?.data || 0

      // 5. 分析现金流（简化版）
      const monthlyExpenses = profile?.monthlyExpenses || 0
      const cashFlow: CashFlowAnalysis = {
        monthlyIncome,
        monthlyExpenses,
        netCashFlow: monthlyIncome - monthlyExpenses,
        emergencyFundRatio: totalAssets > 0 ? (monthlyIncome * 6) / totalAssets : 0,
      }

      // 6. 使用AI生成详细分析和建议
      const analysisPrompt = this.buildAnalysisPrompt({
        totalAssets,
        totalDebts,
        netWorth,
        assetAllocation,
        financialHealthScore,
        cashFlow,
        assets,
        debts,
      })

      const analysisMessages = [...messages, new HumanMessage(analysisPrompt)]
      const aiResponse = await this.generateResponse(analysisMessages)

      // 7. 解析AI建议
      const recommendations = this.extractRecommendations(aiResponse)

      const result: FinancialAnalysisResult = {
        totalAssets,
        totalDebts,
        netWorth,
        assetAllocation,
        financialHealthScore,
        cashFlow,
        recommendations,
        timestamp: new Date(),
      }

      logger.info('财务分析完成', {
        netWorth,
        healthScore: financialHealthScore,
        recommendationsCount: recommendations.length,
      })

      return result
    } catch (error) {
      logger.error('财务分析失败:', error)
      throw error
    }
  }

  protected async updateState(
    state: WorkflowState,
    result: FinancialAnalysisResult
  ): Promise<WorkflowState> {
    return {
      ...state,
      financialAnalysis: result,
      currentStep: 'financial_analysis_complete',
      nextSteps: ['investment_advice', 'risk_assessment'],
      context: {
        ...state.context,
        financialAnalysisComplete: true,
        netWorth: result.netWorth,
        financialHealthScore: result.financialHealthScore,
      },
    }
  }

  private buildAnalysisPrompt(data: {
    totalAssets: number
    totalDebts: number
    netWorth: number
    assetAllocation: AssetAllocation[]
    financialHealthScore: number
    cashFlow: CashFlowAnalysis
    assets: any[]
    debts: any[]
  }): string {
    return `
请基于以下财务数据进行专业分析：

## 财务概况
- 总资产：¥${data.totalAssets.toLocaleString()}
- 总负债：¥${data.totalDebts.toLocaleString()}
- 净资产：¥${data.netWorth.toLocaleString()}
- 财务健康评分：${data.financialHealthScore}/100

## 现金流分析
- 月收入：¥${data.cashFlow.monthlyIncome.toLocaleString()}
- 月支出：¥${data.cashFlow.monthlyExpenses.toLocaleString()}
- 净现金流：¥${data.cashFlow.netCashFlow.toLocaleString()}
- 应急资金比率：${(data.cashFlow.emergencyFundRatio * 100).toFixed(1)}%

## 资产配置
${data.assetAllocation
  .map(
    (item) =>
      `- ${item.category}：¥${item.value.toLocaleString()} (${item.percentage.toFixed(
        1
      )}%, 风险：${item.risk})`
  )
  .join('\n')}

## 具体资产明细
${data.assets
  .map(
    (asset) =>
      `- ${asset.name}：¥${asset.currentValue?.toLocaleString() || 0} (${
        asset.categoryId || '未分类'
      })`
  )
  .join('\n')}

## 债务明细
${data.debts
  .map(
    (debt) =>
      `- ${debt.name}：¥${debt.currentBalance?.toLocaleString() || 0} (利率：${
        debt.interestRate || 'N/A'
      }%)`
  )
  .join('\n')}

请提供：
1. 财务状况综合评估
2. 主要优势和风险点
3. 资产配置合理性分析
4. 债务管理建议
5. 现金流优化建议
6. 具体改善措施（至少5条）

请以专业、客观的语调进行分析，提供具体可行的建议。
    `.trim()
  }

  private extractRecommendations(aiResponse: string): string[] {
    const recommendations: string[] = []

    // 尝试提取建议列表
    const lines = aiResponse.split('\n')
    let inRecommendations = false

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 检测建议部分的开始
      if (
        trimmedLine.includes('建议') ||
        trimmedLine.includes('措施') ||
        trimmedLine.includes('改善') ||
        trimmedLine.includes('优化')
      ) {
        inRecommendations = true
        continue
      }

      // 提取列表项
      if (
        inRecommendations &&
        (trimmedLine.startsWith('-') ||
          trimmedLine.startsWith('•') ||
          trimmedLine.match(/^\d+\./))
      ) {
        const recommendation = trimmedLine.replace(/^[-•\d.]\s*/, '').trim()
        if (recommendation.length > 10) {
          recommendations.push(recommendation)
        }
      }

      // 如果遇到新的段落标题，停止提取
      if (inRecommendations && trimmedLine.startsWith('#')) {
        break
      }
    }

    // 如果没有提取到建议，生成默认建议
    if (recommendations.length === 0) {
      recommendations.push(
        '建议定期审查和更新财务计划',
        '考虑增加应急资金储备',
        '优化资产配置以降低风险',
        '关注债务管理和利率变化',
        '制定明确的财务目标和时间表'
      )
    }

    return recommendations.slice(0, 10) // 最多返回10条建议
  }
}
