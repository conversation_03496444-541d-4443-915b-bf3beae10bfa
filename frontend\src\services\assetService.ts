import { apiClient } from './api'
import type {
  Asset,
  AssetCategory,
  ApiResponse,
  PaginatedResponse,
  PaginationParams,
} from '../types'

export interface CreateAssetRequest {
  categoryId: string
  name: string
  currentValue: number
  purchasePrice: number
  purchaseDate: string
}

export interface UpdateAssetRequest extends Partial<CreateAssetRequest> {}

export interface AssetStatistics {
  totalValue: number
  totalGain: number
  totalGainPercentage: number
  categoryBreakdown: {
    categoryId: string
    categoryName: string
    value: number
    percentage: number
  }[]
}

class AssetService {
  /**
   * 获取用户所有资产
   */
  async getAssets(): Promise<ApiResponse<Asset[]>> {
    return await apiClient.get<Asset[]>('/assets')
  }

  /**
   * 根据分类获取资产
   */
  async getAssetsByCategory(categoryId: string): Promise<ApiResponse<Asset[]>> {
    return await apiClient.get<Asset[]>(`/assets/category/${categoryId}`)
  }

  /**
   * 获取单个资产详情
   */
  async getAsset(id: string): Promise<ApiResponse<Asset>> {
    return await apiClient.get<Asset>(`/assets/${id}`)
  }

  /**
   * 创建新资产
   */
  async createAsset(assetData: CreateAssetRequest): Promise<ApiResponse<Asset>> {
    return await apiClient.post<Asset>('/assets', assetData)
  }

  /**
   * 更新资产
   */
  async updateAsset(
    id: string,
    assetData: UpdateAssetRequest
  ): Promise<ApiResponse<Asset>> {
    return await apiClient.put<Asset>(`/assets/${id}`, assetData)
  }

  /**
   * 删除资产
   */
  async deleteAsset(id: string): Promise<ApiResponse<void>> {
    return await apiClient.delete<void>(`/assets/${id}`)
  }

  /**
   * 获取资产分类
   */
  async getAssetCategories(): Promise<ApiResponse<AssetCategory[]>> {
    return await apiClient.get<AssetCategory[]>('/assets/categories')
  }

  /**
   * 获取用户资产统计
   */
  async getAssetStatistics(): Promise<ApiResponse<AssetStatistics>> {
    return await apiClient.get<AssetStatistics>('/assets/stats')
  }

  /**
   * 获取资产历史记录
   */
  async getAssetHistory(id: string, limit: number = 30): Promise<ApiResponse<any[]>> {
    return await apiClient.get<any[]>(`/assets/${id}/history`, { limit })
  }

  /**
   * 添加资产历史记录
   */
  async addAssetHistory(
    id: string,
    value: number,
    recordDate?: string
  ): Promise<ApiResponse<any>> {
    return await apiClient.post<any>(`/assets/${id}/history`, { value, recordDate })
  }

  /**
   * 批量更新资产价值
   */
  async batchUpdateAssetValues(
    updates: { assetId: string; newValue: number }[]
  ): Promise<ApiResponse<void>> {
    return await apiClient.post<void>('/assets/batch-update', { updates })
  }

  /**
   * 搜索资产
   */
  async searchAssets(query: string): Promise<ApiResponse<Asset[]>> {
    return await apiClient.get<Asset[]>('/assets/search', { q: query })
  }

  /**
   * 导出资产数据
   */
  async exportAssets(format: 'json' | 'csv' = 'json'): Promise<void> {
    await apiClient.download(`/assets/export?format=${format}`, `assets.${format}`)
  }

  /**
   * 导入资产数据
   */
  async importAssets(
    file: File
  ): Promise<ApiResponse<{ imported: number; errors: string[] }>> {
    return await apiClient.upload<{ imported: number; errors: string[] }>(
      '/assets/import',
      file
    )
  }
}

export const assetService = new AssetService()
