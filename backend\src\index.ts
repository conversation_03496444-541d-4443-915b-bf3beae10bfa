import 'dotenv/config'
import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import compression from 'compression'
import morgan from 'morgan'
import { createServer } from 'http'

import { errorHandler } from '@/middleware/errorHandler'
import { notFoundHandler } from '@/middleware/notFoundHandler'
import { rateLimiter } from '@/middleware/rateLimiter'
import { authMiddleware } from '@/middleware/authMiddleware'

// Import routes
import authRoutes from '@/routes/auth.routes'
import assetRoutes from '@/routes/asset.routes'
import debtRoutes from '@/routes/debt.routes'
// import aiRoutes from '@/routes/ai.routes'
import newsRoutes from '@/routes/news.routes'
import marketRoutes from '@/routes/market.routes'
import calendarRoutes from '@/routes/calendar.routes'
// import settingsRoutes from '@/routes/settings.routes'
import dataRoutes from '@/routes/data.routes'
import exportRoutes from '@/routes/export.routes'
import healthRoutes from '@/routes/health.routes'

import { initializeDatabase } from '@/database/connection'
import { logger } from '@/utils/logger'

const app: express.Application = express()
const server = createServer(app)

// 环境变量
const PORT = process.env.PORT || 3001
const NODE_ENV = process.env.NODE_ENV || 'development'
const CORS_ORIGIN = process.env.CORS_ORIGIN || 'http://localhost:5173'

// 中间件配置
app.use(
  helmet({
    crossOriginEmbedderPolicy: false,
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", 'data:', 'https:'],
      },
    },
  })
)

app.use(
  cors({
    origin: CORS_ORIGIN,
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization'],
  })
)

app.use(compression())
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// 日志中间件
if (NODE_ENV === 'development') {
  app.use(morgan('dev'))
} else {
  app.use(morgan('combined'))
}

// 限流中间件
app.use(rateLimiter)

// 健康检查路由（无需认证）
app.use('/api/v1/health', healthRoutes)

// 认证路由（无需认证）
app.use('/api/v1/auth', authRoutes)

// 需要认证的路由
app.use('/api/v1/assets', assetRoutes)
app.use('/api/v1/debts', authMiddleware, debtRoutes)
// app.use('/api/v1/ai', authMiddleware, aiRoutes)
app.use('/api/v1/news', authMiddleware, newsRoutes)
app.use('/api/v1/market', authMiddleware, marketRoutes)
app.use('/api/v1/calendar', authMiddleware, calendarRoutes)
// app.use('/api/v1/settings', authMiddleware, settingsRoutes)
app.use('/api/v1/data', authMiddleware, dataRoutes)
app.use('/api/v1/export', authMiddleware, exportRoutes)

// 404 处理
app.use(notFoundHandler)

// 错误处理中间件
app.use(errorHandler)

// 初始化数据库并启动服务器
async function startServer() {
  try {
    // 初始化数据库
    await initializeDatabase()
    logger.info('数据库初始化成功')

    // 启动服务器
    server.listen(PORT, () => {
      logger.info(`🚀 服务器启动成功`)
      logger.info(`📍 端口: ${PORT}`)
      logger.info(`🌍 环境: ${NODE_ENV}`)
      logger.info(`🔗 CORS源: ${CORS_ORIGIN}`)

      if (NODE_ENV === 'development') {
        logger.info(`📖 API文档: http://localhost:${PORT}/api/v1/health`)
      }
    })

    // 优雅关闭
    process.on('SIGTERM', gracefulShutdown)
    process.on('SIGINT', gracefulShutdown)
  } catch (error) {
    logger.error('服务器启动失败:', error)
    process.exit(1)
  }
}

// 优雅关闭函数
function gracefulShutdown(signal: string) {
  logger.info(`收到 ${signal} 信号，开始优雅关闭...`)

  server.close(() => {
    logger.info('HTTP服务器已关闭')
    process.exit(0)
  })

  // 强制关闭超时
  setTimeout(() => {
    logger.error('强制关闭服务器')
    process.exit(1)
  }, 10000)
}

// 未捕获异常处理
process.on('uncaughtException', (error) => {
  logger.error('未捕获异常:', error)
  process.exit(1)
})

process.on('unhandledRejection', (reason, promise) => {
  logger.error('未处理的Promise拒绝:', reason)
  process.exit(1)
})

// 启动服务器
startServer()

export default app
