# AI 驱动资产管理应用 - 完整架构设计文档

## 文档概览

本文档为 AI 驱动的资产管理和理财投资建议网页应用提供完整的架构设计指导，涵盖技术选型、系统架构、数据库设计、API 规范、部署方案、技术挑战解决方案和项目结构建议。

**文档版本**: 1.0  
**创建日期**: 2024 年 1 月  
**目标读者**: 开发团队、项目经理、技术决策者

## 1. 项目需求分析

### 1.1 核心功能需求

- **资产管理模块**: 现金、投资、房产、汽车等资产的管理和可视化
- **负债管理模块**: 信用卡、房贷、车贷等负债的跟踪和还款计划
- **AI Agent 系统**: 支持多模型切换的智能投资建议引擎
- **新闻源集成**: 通过 MCP Server 接入金融新闻并进行相关性筛选
- **市场数据更新**: 每日更新主要股指、金价等市场指标
- **日历功能**: 管理财务相关的未来事件和提醒

### 1.2 技术约束

- **部署模式**: 主要面向自部署的小型应用
- **用户规模**: 100-1000 用户，低并发访问
- **技术栈**: React 前端 + Node.js 后端 + TypeScript 全栈
- **数据库**: SQLite（便于自部署）
- **包管理**: pnpm
- **开发环境**: Windows

### 1.3 非功能性需求

- **性能**: API 响应时间 < 2 秒，页面加载时间 < 3 秒
- **可用性**: 99.5%系统可用性
- **安全性**: 数据加密存储，API Key 安全管理
- **可扩展性**: 模块化设计，支持功能插件扩展
- **易用性**: 直观的用户界面，流畅的交互体验

## 2. 技术栈选择

### 2.1 前端技术栈

| 技术        | 选择                      | 理由                   |
| ----------- | ------------------------- | ---------------------- |
| 框架        | React 18+ with TypeScript | 成熟生态，类型安全     |
| 状态管理    | Zustand                   | 轻量级，适合小型应用   |
| UI 组件库   | Ant Design                | 丰富的组件，企业级应用 |
| 图表库      | Recharts                  | React 原生，性能优秀   |
| 路由        | React Router v6           | 标准路由解决方案       |
| HTTP 客户端 | Axios                     | 功能完善，拦截器支持   |
| 构建工具    | Vite                      | 快速开发体验           |
| 样式方案    | Tailwind CSS              | 原子化 CSS，开发效率高 |

### 2.2 后端技术栈

| 技术     | 选择                        | 理由                    |
| -------- | --------------------------- | ----------------------- |
| 运行时   | Node.js 18+ with TypeScript | 与前端技术栈统一        |
| Web 框架 | Express.js                  | 简单可靠，生态丰富      |
| AI 集成  | Langgraph.js                | 强大的 Agent 工作流     |
| 数据库   | SQLite3 with better-sqlite3 | 自部署友好，性能优秀    |
| ORM      | Drizzle ORM                 | TypeScript 原生，轻量级 |
| 认证     | JWT + bcrypt                | 无状态认证              |
| 任务调度 | node-cron                   | 定时任务支持            |
| API 文档 | Swagger/OpenAPI 3.0         | 标准化 API 文档         |

### 2.3 开发工具链

- **包管理器**: pnpm（性能优秀，磁盘空间节省）
- **代码规范**: ESLint + Prettier
- **类型检查**: TypeScript strict mode
- **测试框架**: Jest + React Testing Library
- **Git 钩子**: Husky + lint-staged
- **环境管理**: dotenv

## 3. 系统架构设计

### 3.1 整体架构图

```mermaid
graph TB
    subgraph "客户端层 Client Layer"
        Browser[Web Browser]
        Mobile[Mobile Browser]
    end

    subgraph "前端层 Frontend Layer"
        React[React Application]
        Store[Zustand Store]
        Components[UI Components]
        Charts[Chart Components]
    end

    subgraph "API网关层 API Gateway"
        Gateway[Express.js Server]
        Auth[JWT Authentication]
        RateLimit[Rate Limiting]
        CORS[CORS Middleware]
    end

    subgraph "业务服务层 Business Services"
        AssetService[资产管理服务]
        DebtService[负债管理服务]
        AIService[AI Agent服务]
        NewsService[新闻服务]
        MarketService[市场数据服务]
        CalendarService[日历服务]
    end

    subgraph "AI引擎层 AI Engine"
        LangGraph[Langgraph.js]
        ModelManager[模型管理器]
        AgentWorkflow[Agent工作流]
    end

    subgraph "数据访问层 Data Access"
        ORM[Drizzle ORM]
        Repository[Repository Pattern]
        Cache[内存缓存]
    end

    subgraph "数据存储层 Data Storage"
        SQLite[(SQLite Database)]
        Files[文件存储]
        Logs[日志文件]
    end

    subgraph "外部服务 External Services"
        NewsAPI[新闻API MCP Server]
        FinanceAPI[金融数据API]
        AIModels[AI模型APIs]
        SMTP[邮件服务]
    end

    subgraph "定时任务 Scheduled Jobs"
        Cron[Node-cron]
        DataUpdater[数据更新任务]
        Backup[备份任务]
    end

    Browser --> React
    Mobile --> React
    React --> Store
    React --> Components
    React --> Charts

    React --> Gateway
    Gateway --> Auth
    Gateway --> RateLimit
    Gateway --> CORS

    Gateway --> AssetService
    Gateway --> DebtService
    Gateway --> AIService
    Gateway --> NewsService
    Gateway --> MarketService
    Gateway --> CalendarService

    AIService --> LangGraph
    LangGraph --> ModelManager
    LangGraph --> AgentWorkflow

    AssetService --> ORM
    DebtService --> ORM
    NewsService --> ORM
    MarketService --> ORM
    CalendarService --> ORM

    ORM --> Repository
    Repository --> Cache
    Repository --> SQLite

    SQLite --> Files
    Files --> Logs

    NewsService --> NewsAPI
    MarketService --> FinanceAPI
    ModelManager --> AIModels
    CalendarService --> SMTP

    Cron --> DataUpdater
    Cron --> Backup
    DataUpdater --> MarketService
    DataUpdater --> NewsService
```

### 3.2 核心模块设计

#### 3.2.1 前端架构

**组件层次结构**:

- **页面组件**: Dashboard, Assets, Debts, AI Chat, News, Calendar, Settings
- **布局组件**: Header, Sidebar, Footer, Layout
- **业务组件**: AssetChart, DebtChart, ChatInterface, NewsCard
- **基础组件**: Button, Input, Modal, Table, Loading

**状态管理**:

- **用户状态**: 认证信息、用户配置、API Keys
- **业务状态**: 资产数据、负债数据、AI 对话、新闻数据、市场数据
- **UI 状态**: 加载状态、错误状态、模态框状态

#### 3.2.2 后端架构

**分层架构**:

- **控制器层**: 处理 HTTP 请求，参数验证，响应格式化
- **服务层**: 业务逻辑处理，事务管理，外部服务调用
- **仓库层**: 数据访问抽象，查询优化，缓存管理
- **数据层**: SQLite 数据库，文件存储，日志记录

**AI Agent 系统**:

- **模型管理**: 多 AI 模型封装，API Key 管理，成本控制
- **Agent 工作流**: 投资建议、资产分析、新闻分析、财务规划
- **工具函数**: 数据查询、计算工具、图表生成、新闻检索

## 4. 数据库设计

### 4.1 核心实体关系

```mermaid
erDiagram
    Users ||--o{ Assets : owns
    Users ||--o{ Debts : has
    Users ||--o{ AIConversations : creates
    Users ||--o{ CalendarEvents : schedules
    Users ||--o{ UserSettings : configures

    Assets ||--o{ AssetHistory : tracks
    Assets }o--|| AssetCategories : belongs_to

    Debts ||--o{ DebtPayments : has
    Debts }o--|| DebtTypes : belongs_to

    AIConversations ||--o{ AIMessages : contains
    AIMessages }o--|| AIModels : uses

    News ||--o{ NewsCategories : categorized_by
    MarketData }o--|| MarketIndicators : belongs_to

    Users {
        string id PK
        string email UK
        string password_hash
        string name
        timestamp created_at
    }

    Assets {
        string id PK
        string user_id FK
        string category_id FK
        string name
        decimal current_value
        decimal purchase_price
        date purchase_date
        timestamp created_at
    }

    Debts {
        string id PK
        string user_id FK
        string type_id FK
        string name
        decimal principal_amount
        decimal current_balance
        decimal interest_rate
        timestamp created_at
    }
```

### 4.2 关键表结构

#### 用户管理

- **users**: 用户基本信息
- **user_settings**: 用户配置（API Keys、偏好设置）

#### 资产管理

- **asset_categories**: 资产分类（现金、股票、房产等）
- **assets**: 用户资产记录
- **asset_history**: 资产价值历史

#### 负债管理

- **debt_types**: 负债类型（信用卡、房贷、车贷等）
- **debts**: 用户负债记录
- **debt_payments**: 还款记录

#### AI 系统

- **ai_models**: AI 模型配置
- **ai_conversations**: 对话会话
- **ai_messages**: 对话消息

#### 新闻和市场数据

- **news**: 新闻文章
- **news_categories**: 新闻分类
- **market_indicators**: 市场指标定义
- **market_data**: 市场数据记录

## 5. API 接口设计

### 5.1 API 设计原则

- **RESTful 设计**: 遵循 REST 架构风格
- **统一响应格式**: 标准 JSON 响应结构
- **版本控制**: URL 路径版本控制 `/api/v1/`
- **认证授权**: JWT Token 认证
- **错误处理**: 标准 HTTP 状态码
- **分页支持**: 列表接口统一分页

### 5.2 核心 API 端点

#### 认证模块

```
POST /api/v1/auth/register    # 用户注册
POST /api/v1/auth/login       # 用户登录
POST /api/v1/auth/refresh     # 刷新Token
GET  /api/v1/auth/profile     # 获取用户信息
```

#### 资产管理

```
GET    /api/v1/assets         # 获取资产列表
POST   /api/v1/assets         # 创建资产
PUT    /api/v1/assets/:id     # 更新资产
DELETE /api/v1/assets/:id     # 删除资产
GET    /api/v1/assets/statistics  # 资产统计
```

#### AI 对话

```
POST /api/v1/ai/conversations     # 创建对话
POST /api/v1/ai/conversations/:id/messages  # 发送消息
GET  /api/v1/ai/conversations/:id/stream    # 流式响应(SSE)
GET  /api/v1/ai/models            # 获取可用模型
```

#### 市场数据

```
GET /api/v1/market/indicators     # 获取市场指标
GET /api/v1/market/indicators/:id/history  # 历史数据
```

### 5.3 响应格式标准

**成功响应**:

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

**错误响应**:

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 6. 部署架构

### 6.1 推荐部署方案

#### Docker 一键部署（推荐）

```yaml
# docker-compose.yml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - '3000:80'
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - '3001:3001'
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/database.sqlite
```

#### 部署架构图

```mermaid
graph TB
    subgraph "Docker Host"
        subgraph "容器服务"
            Frontend[Frontend Container<br/>Nginx + React]
            Backend[Backend Container
```

## 6. 部署架构（续）

### 6.2 部署架构图

```mermaid
graph TB
    subgraph "Docker Host"
        subgraph "容器服务"
            Frontend[Frontend Container<br/>Nginx + React]
            Backend[Backend Container<br/>Node.js + TypeScript]
            Database[SQLite Volume<br/>持久化存储]
        end

        subgraph "外部依赖"
            NewsAPI[新闻MCP Server]
            FinanceAPI[金融数据API]
            AIModels[AI模型APIs]
        end
    end

    User[用户] --> Frontend
    Frontend --> Backend
    Backend --> Database
    Backend --> NewsAPI
    Backend --> FinanceAPI
    Backend --> AIModels
```

### 6.3 部署选项对比

| 部署方式        | 适用场景     | 技术要求        | 优势               | 劣势             |
| --------------- | ------------ | --------------- | ------------------ | ---------------- |
| Docker 一键部署 | 大多数用户   | Docker 基础知识 | 简单快速，环境一致 | 需要 Docker 环境 |
| 本地开发部署    | 开发者       | Node.js, pnpm   | 完全控制，易调试   | 环境配置复杂     |
| 云服务器部署    | 远程访问需求 | 服务器管理      | 高可用，远程访问   | 成本较高         |

## 7. 关键技术挑战总结

### 7.1 AI 集成挑战

- **多模型管理**: 统一接口抽象，模型切换机制
- **工作流设计**: Langgraph.js Agent 工作流实现
- **成本控制**: 使用量监控和限制机制
- **API Key 安全**: 加密存储和权限管理

### 7.2 性能优化挑战

- **数据库优化**: SQLite 性能调优和索引策略
- **前端渲染**: 大量数据可视化和虚拟化
- **实时更新**: WebSocket 和定时任务协调
- **缓存策略**: 多层缓存提升响应速度

### 7.3 安全性挑战

- **数据加密**: 敏感信息的存储和传输加密
- **认证授权**: JWT Token 和权限控制
- **API 安全**: 请求签名和防重放攻击
- **输入验证**: 严格的参数验证和 SQL 注入防护

## 8. 开发和部署指南

### 8.1 快速开始

```bash
# 1. 环境准备
git clone <repository-url>
cd ai-asset-manager
cp .env.example .env

# 2. 依赖安装
pnpm install

# 3. 数据库初始化
pnpm run db:migrate
pnpm run db:seed

# 4. 启动开发环境
pnpm run dev
```

### 8.2 生产部署

```bash
# Docker部署（推荐）
docker-compose up -d

# 或手动部署
pnpm run build
pnpm run start:prod
```

## 9. 项目成功关键因素

### 9.1 技术层面

- **合理的技术选型**: 适合团队能力和项目规模
- **清晰的架构设计**: 模块化、可扩展的系统架构
- **完善的开发流程**: 代码规范、测试覆盖、CI/CD
- **有效的性能优化**: 数据库、前端、AI 服务优化

### 9.2 管理层面

- **需求管理**: 明确的功能需求和优先级
- **团队协作**: 良好的沟通和协作机制
- **质量保证**: 代码审查和测试流程
- **持续改进**: 基于用户反馈的迭代优化

## 10. 总结与建议

本架构设计为 AI 驱动的资产管理应用提供了完整的技术解决方案，具有以下特点：

### 10.1 核心优势

- **自部署友好**: SQLite + Docker，降低部署门槛
- **技术栈统一**: TypeScript 全栈，提高开发效率
- **AI 能力强大**: 多模型支持，智能投资建议
- **安全性完善**: 多层次安全防护机制
- **可扩展性好**: 模块化设计，支持功能扩展

### 10.2 实施建议

1. **分阶段开发**: 先实现核心功能，再完善高级特性
2. **重视测试**: 建立完善的单元测试和集成测试
3. **用户反馈**: 早期用户参与，快速迭代优化
4. **文档维护**: 保持技术文档与代码同步更新
5. **性能监控**: 建立完善的监控和告警机制

### 10.3 风险控制

- **技术风险**: 多方案备份，降低单点故障风险
- **安全风险**: 多层防护，定期安全审计
- **性能风险**: 持续监控，提前扩容规划
- **业务风险**: 用户需求验证，快速响应变化

通过遵循本架构设计文档，开发团队可以构建一个功能完善、性能优秀、安全可靠的 AI 驱动资产管理应用。

---

**文档版本**: v1.0  
**最后更新**: 2024 年 1 月  
**维护说明**: 本文档应随项目发展持续更新，确保架构设计与实际实现保持一致。
