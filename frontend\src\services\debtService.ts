import { apiClient } from '../utils/apiClient'
import type { ApiResponse } from '../types'

// 负债相关类型定义
export interface Debt {
  id: string
  typeId: string
  name: string
  principalAmount: number
  currentBalance: number
  interestRate: number
  startDate: string
  monthlyPayment?: number
  currency: string
  createdAt: string
  updatedAt: string
}

export interface DebtType {
  id: string
  name: string
  description?: string
  icon: string
  color: string
}

export interface DebtPayment {
  id: string
  debtId: string
  amount: number
  principalAmount: number
  interestAmount: number
  paymentDate: string
  createdAt: string
}

export interface DebtStatistics {
  totalDebts: number
  totalBalance: number
  totalMonthlyPayment: number
  debtsByType: Array<{
    typeId: string
    typeName: string
    count: number
    totalBalance: number
    averageInterestRate: number
  }>
}

export interface CreateDebtRequest {
  name: string
  typeId: string
  principalAmount: number
  currentBalance: number
  interestRate: number
  startDate: string
  monthlyPayment?: number
  currency?: string
}

export interface UpdateDebtRequest {
  name?: string
  typeId?: string
  principalAmount?: number
  currentBalance?: number
  interestRate?: number
  startDate?: string
  monthlyPayment?: number
  currency?: string
}

export interface CreatePaymentRequest {
  amount: number
  principalAmount: number
  interestAmount: number
  paymentDate?: string
}

class DebtService {
  /**
   * 获取用户所有负债
   */
  async getDebts(): Promise<ApiResponse<Debt[]>> {
    return await apiClient.get<Debt[]>('/debts')
  }

  /**
   * 根据类型获取负债
   */
  async getDebtsByType(typeId: string): Promise<ApiResponse<Debt[]>> {
    return await apiClient.get<Debt[]>(`/debts?type=${typeId}`)
  }

  /**
   * 获取单个负债详情
   */
  async getDebt(id: string): Promise<ApiResponse<Debt>> {
    return await apiClient.get<Debt>(`/debts/${id}`)
  }

  /**
   * 创建新负债
   */
  async createDebt(debtData: CreateDebtRequest): Promise<ApiResponse<Debt>> {
    return await apiClient.post<Debt>('/debts', debtData)
  }

  /**
   * 更新负债
   */
  async updateDebt(
    id: string,
    debtData: UpdateDebtRequest
  ): Promise<ApiResponse<Debt>> {
    return await apiClient.put<Debt>(`/debts/${id}`, debtData)
  }

  /**
   * 删除负债
   */
  async deleteDebt(id: string): Promise<ApiResponse<void>> {
    return await apiClient.delete<void>(`/debts/${id}`)
  }

  /**
   * 获取负债类型
   */
  async getDebtTypes(): Promise<ApiResponse<DebtType[]>> {
    return await apiClient.get<DebtType[]>('/debts/types')
  }

  /**
   * 获取负债统计
   */
  async getDebtStatistics(): Promise<ApiResponse<DebtStatistics>> {
    return await apiClient.get<DebtStatistics>('/debts/stats')
  }

  /**
   * 记录还款
   */
  async recordPayment(
    debtId: string,
    paymentData: CreatePaymentRequest
  ): Promise<ApiResponse<DebtPayment>> {
    return await apiClient.post<DebtPayment>(`/debts/${debtId}/payments`, paymentData)
  }

  /**
   * 获取还款历史
   */
  async getPaymentHistory(debtId: string): Promise<ApiResponse<DebtPayment[]>> {
    return await apiClient.get<DebtPayment[]>(`/debts/${debtId}/payments`)
  }

  /**
   * 搜索负债
   */
  async searchDebts(query: string): Promise<ApiResponse<Debt[]>> {
    return await apiClient.get<Debt[]>('/debts/search', { q: query })
  }

  /**
   * 导出负债数据
   */
  async exportDebts(format: 'json' | 'csv' = 'json'): Promise<void> {
    await apiClient.download(`/debts/export?format=${format}`, `debts.${format}`)
  }

  /**
   * 计算负债健康度评分
   */
  calculateDebtHealthScore(debts: Debt[]): {
    score: number
    level: 'excellent' | 'good' | 'fair' | 'poor'
    factors: Array<{
      name: string
      score: number
      weight: number
      description: string
    }>
  } {
    if (debts.length === 0) {
      return {
        score: 100,
        level: 'excellent',
        factors: [],
      }
    }

    const factors = []
    let totalScore = 0
    let totalWeight = 0

    // 因子1: 负债总额占比 (权重: 30%)
    const totalBalance = debts.reduce((sum, debt) => sum + debt.currentBalance, 0)
    const debtRatioScore = Math.max(0, 100 - (totalBalance / 1000000) * 50) // 假设100万为基准
    factors.push({
      name: '负债总额',
      score: debtRatioScore,
      weight: 0.3,
      description: '负债总额相对合理范围的评分',
    })
    totalScore += debtRatioScore * 0.3
    totalWeight += 0.3

    // 因子2: 平均利率 (权重: 25%)
    const avgInterestRate = debts.reduce((sum, debt) => sum + debt.interestRate, 0) / debts.length
    const interestRateScore = Math.max(0, 100 - avgInterestRate * 1000) // 利率越低越好
    factors.push({
      name: '平均利率',
      score: interestRateScore,
      weight: 0.25,
      description: '负债平均利率水平评分',
    })
    totalScore += interestRateScore * 0.25
    totalWeight += 0.25

    // 因子3: 负债多样性 (权重: 20%)
    const uniqueTypes = new Set(debts.map(debt => debt.typeId)).size
    const diversityScore = Math.min(100, uniqueTypes * 25) // 类型越多样化越好，但有上限
    factors.push({
      name: '负债多样性',
      score: diversityScore,
      weight: 0.2,
      description: '负债类型分散程度评分',
    })
    totalScore += diversityScore * 0.2
    totalWeight += 0.2

    // 因子4: 还款能力 (权重: 25%)
    const totalMonthlyPayment = debts.reduce((sum, debt) => sum + (debt.monthlyPayment || 0), 0)
    const paymentCapacityScore = totalMonthlyPayment > 0 ? Math.min(100, (50000 / totalMonthlyPayment) * 100) : 50
    factors.push({
      name: '还款能力',
      score: paymentCapacityScore,
      weight: 0.25,
      description: '月还款额相对收入的合理性评分',
    })
    totalScore += paymentCapacityScore * 0.25
    totalWeight += 0.25

    const finalScore = totalWeight > 0 ? totalScore / totalWeight : 0

    let level: 'excellent' | 'good' | 'fair' | 'poor'
    if (finalScore >= 80) level = 'excellent'
    else if (finalScore >= 60) level = 'good'
    else if (finalScore >= 40) level = 'fair'
    else level = 'poor'

    return {
      score: Math.round(finalScore),
      level,
      factors,
    }
  }

  /**
   * 计算负债偿还计划
   */
  calculatePayoffPlan(debt: Debt): {
    monthsToPayoff: number
    totalInterest: number
    monthlyPayment: number
    payoffDate: string
  } {
    const monthlyRate = debt.interestRate / 12
    const monthlyPayment = debt.monthlyPayment || debt.currentBalance * 0.02 // 默认2%

    if (monthlyPayment <= debt.currentBalance * monthlyRate) {
      // 如果月还款额小于等于月利息，永远还不完
      return {
        monthsToPayoff: Infinity,
        totalInterest: Infinity,
        monthlyPayment,
        payoffDate: '永远无法还清',
      }
    }

    const monthsToPayoff = Math.ceil(
      Math.log(1 + (debt.currentBalance * monthlyRate) / monthlyPayment) /
      Math.log(1 + monthlyRate)
    )

    const totalPayment = monthlyPayment * monthsToPayoff
    const totalInterest = totalPayment - debt.currentBalance

    const payoffDate = new Date()
    payoffDate.setMonth(payoffDate.getMonth() + monthsToPayoff)

    return {
      monthsToPayoff,
      totalInterest,
      monthlyPayment,
      payoffDate: payoffDate.toISOString().split('T')[0],
    }
  }
}

// 导出单例实例
export const debtService = new DebtService()
