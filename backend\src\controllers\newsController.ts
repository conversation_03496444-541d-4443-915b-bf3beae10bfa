import { Request, Response } from 'express'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'

export class NewsController {
  /**
   * 获取新闻列表
   */
  async getNews(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { page = 1, limit = 20, category } = req.query

      // 模拟新闻数据
      const mockNews = [
        {
          id: '1',
          title: '美股三大指数收盘涨跌不一',
          summary: '道琼斯指数上涨0.5%，纳斯达克指数下跌0.2%，标普500指数基本持平',
          url: 'https://example.com/news/1',
          source: '财经新闻',
          publishedDate: new Date().toISOString().split('T')[0],
          relevanceScore: 0.85,
          createdAt: new Date(),
        },
        {
          id: '2',
          title: '央行宣布降准0.25个百分点',
          summary: '为支持实体经济发展，央行决定下调存款准备金率',
          url: 'https://example.com/news/2',
          source: '央行公告',
          publishedDate: new Date().toISOString().split('T')[0],
          relevanceScore: 0.92,
          createdAt: new Date(),
        },
        {
          id: '3',
          title: '科技股表现强劲，AI概念股领涨',
          summary: '人工智能相关股票今日表现突出，多只个股涨幅超过5%',
          url: 'https://example.com/news/3',
          source: '科技日报',
          publishedDate: new Date().toISOString().split('T')[0],
          relevanceScore: 0.78,
          createdAt: new Date(),
        },
      ]

      const pageNum = parseInt(page as string, 10)
      const limitNum = parseInt(limit as string, 10)
      const startIndex = (pageNum - 1) * limitNum
      const endIndex = startIndex + limitNum

      const paginatedNews = mockNews.slice(startIndex, endIndex)

      res.json({
        success: true,
        data: paginatedNews,
        pagination: {
          page: pageNum,
          limit: limitNum,
          total: mockNews.length,
          totalPages: Math.ceil(mockNews.length / limitNum),
          hasNext: endIndex < mockNews.length,
          hasPrev: pageNum > 1,
        },
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取新闻失败:', error)
      res.status(500).json({
        success: false,
        message: '获取新闻失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取单条新闻详情
   */
  async getNewsById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { newsId } = req.params

      // 模拟新闻详情
      const mockNewsDetail = {
        id: newsId,
        title: '美股三大指数收盘涨跌不一',
        content: `
          美东时间周一，美股三大指数收盘涨跌不一。道琼斯工业平均指数上涨0.5%，
          收于34,500点；纳斯达克综合指数下跌0.2%，收于14,200点；
          标普500指数基本持平，收于4,400点。
          
          市场分析师认为，当前市场正在消化美联储政策预期的变化，
          投资者对经济前景保持谨慎乐观态度。科技股表现分化，
          部分大型科技公司股价出现调整。
        `,
        summary: '道琼斯指数上涨0.5%，纳斯达克指数下跌0.2%，标普500指数基本持平',
        url: `https://example.com/news/${newsId}`,
        source: '财经新闻',
        publishedDate: new Date().toISOString().split('T')[0],
        relevanceScore: 0.85,
        createdAt: new Date(),
      }

      res.json({
        success: true,
        data: mockNewsDetail,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取新闻详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取新闻详情失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取新闻分类
   */
  async getNewsCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = [
        { id: 'market', name: '市场动态', description: '股市、债市等金融市场新闻' },
        { id: 'policy', name: '政策解读', description: '货币政策、财政政策等' },
        { id: 'company', name: '公司新闻', description: '上市公司动态' },
        { id: 'economy', name: '宏观经济', description: '经济数据、经济分析' },
        { id: 'international', name: '国际财经', description: '国际市场动态' },
      ]

      res.json({
        success: true,
        data: categories,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取新闻分类失败:', error)
      res.status(500).json({
        success: false,
        message: '获取新闻分类失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const newsController = new NewsController()
