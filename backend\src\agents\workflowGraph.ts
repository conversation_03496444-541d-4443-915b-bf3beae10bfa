import { StateGraph, START, END, Annotation } from '@langchain/langgraph'
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages'
import { logger } from '../utils/logger'
import type {
  WorkflowExecutionResult,
  StateGraphExecutionContext,
  StateGraphWorkflowInstance,
} from '../types/agents'

// 导入所有代理
import { FinancialAnalysisAgent } from './financialAnalysisAgent'
import { InvestmentAdvisorAgent } from './investmentAdvisorAgent'
import { RiskAssessmentAgent } from './riskAssessmentAgent'
import { NewsAnalysisAgent } from './newsAnalysisAgent'
import { CoordinatorAgent } from './coordinatorAgent'

// 定义StateGraph状态注解
const WorkflowStateAnnotation = Annotation.Root({
  messages: Annotation<BaseMessage[]>({
    reducer: (x: BaseMessage[], y: BaseMessage[]) => x.concat(y),
    default: () => [],
  }),
  userQuery: Annotation<string>({
    reducer: (x: string, y: string) => y || x,
    default: () => '',
  }),
  userId: Annotation<string>({
    reducer: (x: string, y: string) => y || x,
    default: () => '',
  }),
  conversationId: Annotation<string>({
    reducer: (x: string, y: string) => y || x,
    default: () => '',
  }),
  modelId: Annotation<string>({
    reducer: (x: string, y: string) => y || x,
    default: () => 'deepseek-chat',
  }),
  userAssets: Annotation<any[]>({
    reducer: (x: any[], y: any[]) => y || x,
    default: () => [],
  }),
  userDebts: Annotation<any[]>({
    reducer: (x: any[], y: any[]) => y || x,
    default: () => [],
  }),
  userProfile: Annotation<any>({
    reducer: (x: any, y: any) => y || x,
    default: () => null,
  }),
  financialAnalysis: Annotation<any>({
    reducer: (x: any, y: any) => y || x,
    default: () => undefined,
  }),
  investmentAdvice: Annotation<any>({
    reducer: (x: any, y: any) => y || x,
    default: () => undefined,
  }),
  riskAssessment: Annotation<any>({
    reducer: (x: any, y: any) => y || x,
    default: () => undefined,
  }),
  newsAnalysis: Annotation<any>({
    reducer: (x: any, y: any) => y || x,
    default: () => undefined,
  }),
  currentStep: Annotation<string>({
    reducer: (x: string, y: string) => y || x,
    default: () => 'start',
  }),
  nextSteps: Annotation<string[]>({
    reducer: (x: string[], y: string[]) => y || x,
    default: () => [],
  }),
  isComplete: Annotation<boolean>({
    reducer: (x: boolean, y: boolean) => (y !== undefined ? y : x),
    default: () => false,
  }),
  error: Annotation<string | undefined>({
    reducer: (x: string | undefined, y: string | undefined) => y || x,
    default: () => undefined,
  }),
  context: Annotation<Record<string, any>>({
    reducer: (x: Record<string, any>, y: Record<string, any>) => ({ ...x, ...y }),
    default: () => ({}),
  }),
  memory: Annotation<any>({
    reducer: (x: any, y: any) => y || x,
    default: () => ({
      conversationHistory: [],
      userPreferences: {
        riskTolerance: 'moderate',
        investmentGoals: [],
        timeHorizon: '1-3年',
        preferredAssetTypes: [],
        excludedAssetTypes: [],
      },
      previousAnalyses: [],
      learnings: [],
    }),
  }),
  nodeResults: Annotation<Record<string, any>>({
    reducer: (x: Record<string, any>, y: Record<string, any>) => ({ ...x, ...y }),
    default: () => ({}),
  }),
  executionPath: Annotation<string[]>({
    reducer: (x: string[], y: string[]) => x.concat(y || []),
    default: () => [],
  }),
  retryCount: Annotation<number>({
    reducer: (x: number, y: number) => (y !== undefined ? y : x),
    default: () => 0,
  }),
  lastNodeExecuted: Annotation<string>({
    reducer: (x: string, y: string) => y || x,
    default: () => '',
  }),
  conditionalFlags: Annotation<Record<string, boolean>>({
    reducer: (x: Record<string, boolean>, y: Record<string, boolean>) => ({
      ...x,
      ...y,
    }),
    default: () => ({}),
  }),
})

// 定义状态类型
type WorkflowState = typeof WorkflowStateAnnotation.State

/**
 * 基于LangGraph StateGraph的财务分析工作流
 */
export class FinancialWorkflowStateGraph {
  private graph: any
  private compiledGraph: any
  private agents: Map<string, any> = new Map()
  private executionContext: StateGraphExecutionContext
  private workflowInstance: StateGraphWorkflowInstance | null = null

  constructor(modelId: string = 'deepseek-chat') {
    // 初始化代理
    this.initializeAgents(modelId)

    // 创建StateGraph
    this.graph = new StateGraph(WorkflowStateAnnotation)

    // 构建工作流图
    this.buildWorkflowGraph()

    // 编译图
    this.compiledGraph = this.graph.compile()

    // 初始化执行上下文
    this.executionContext = {
      graphId: 'financial_workflow',
      executionId: '',
      startTime: new Date(),
      visitedNodes: [],
      nodeExecutionTimes: {},
      errors: [],
      checkpoints: [],
    }
  }

  /**
   * 初始化所有代理
   */
  private initializeAgents(modelId: string): void {
    this.agents.set('financial_analysis', new FinancialAnalysisAgent(modelId))
    this.agents.set('investment_advisor', new InvestmentAdvisorAgent(modelId))
    this.agents.set('risk_assessment', new RiskAssessmentAgent(modelId))
    this.agents.set('news_analysis', new NewsAnalysisAgent(modelId))
    this.agents.set('coordinator', new CoordinatorAgent(modelId))
  }

  /**
   * 构建StateGraph工作流图
   */
  private buildWorkflowGraph(): void {
    // 添加节点
    this.graph
      .addNode('financial_analysis', this.createFinancialAnalysisNode())
      .addNode('investment_advisor', this.createInvestmentAdvisorNode())
      .addNode('risk_assessment', this.createRiskAssessmentNode())
      .addNode('news_analysis', this.createNewsAnalysisNode())
      .addNode('coordinator', this.createCoordinatorNode())
      .addNode('error_handler', this.createErrorHandlerNode())

    // 设置入口点
    this.graph.addEdge(START, 'financial_analysis')

    // 添加条件边 - 财务分析完成后的路由
    this.graph.addConditionalEdges(
      'financial_analysis',
      this.createRoutingCondition(),
      {
        continue: 'investment_advisor',
        error: 'error_handler',
      }
    )

    // 顺序执行分析节点
    this.graph.addEdge('investment_advisor', 'risk_assessment')
    this.graph.addEdge('risk_assessment', 'news_analysis')
    this.graph.addEdge('news_analysis', 'coordinator')

    // 协调节点完成后结束
    this.graph.addConditionalEdges('coordinator', this.createCompletionCondition(), {
      complete: END,
      retry: 'financial_analysis',
      error: 'error_handler',
    })

    // 错误处理节点
    this.graph.addEdge('error_handler', END)
  }

  /**
   * 创建财务分析节点
   */
  private createFinancialAnalysisNode() {
    return async (state: WorkflowState): Promise<Partial<WorkflowState>> => {
      const startTime = Date.now()

      try {
        logger.info('执行财务分析节点')

        const agent = this.agents.get('financial_analysis')
        if (!agent) {
          throw new Error('财务分析代理未找到')
        }

        // 执行代理
        const updatedState = await agent.execute(state)

        const executionTime = Date.now() - startTime
        this.executionContext.nodeExecutionTimes['financial_analysis'] = executionTime
        this.executionContext.visitedNodes.push('financial_analysis')

        logger.info('财务分析节点执行完成', { executionTime })

        return {
          ...updatedState,
          currentStep: 'financial_analysis_complete',
          lastNodeExecuted: 'financial_analysis',
          executionPath: [...(state.executionPath || []), 'financial_analysis'],
          nodeResults: {
            ...state.nodeResults,
            financial_analysis: {
              success: true,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      } catch (error) {
        const executionTime = Date.now() - startTime
        const errorMessage =
          error instanceof Error ? error.message : '财务分析节点执行失败'

        logger.error('财务分析节点执行失败:', error)

        this.executionContext.errors.push({
          nodeId: 'financial_analysis',
          error: errorMessage,
          timestamp: new Date(),
          retryAttempt: state.retryCount || 0,
          recoverable: true,
        })

        return {
          error: errorMessage,
          currentStep: 'error',
          lastNodeExecuted: 'financial_analysis',
          executionPath: [...(state.executionPath || []), 'financial_analysis'],
          nodeResults: {
            ...state.nodeResults,
            financial_analysis: {
              success: false,
              error: errorMessage,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      }
    }
  }

  /**
   * 创建投资建议节点
   */
  private createInvestmentAdvisorNode() {
    return async (state: WorkflowState): Promise<Partial<WorkflowState>> => {
      const startTime = Date.now()

      try {
        logger.info('执行投资建议节点')

        const agent = this.agents.get('investment_advisor')
        if (!agent) {
          throw new Error('投资建议代理未找到')
        }

        const updatedState = await agent.execute(state)

        const executionTime = Date.now() - startTime
        this.executionContext.nodeExecutionTimes['investment_advisor'] = executionTime
        this.executionContext.visitedNodes.push('investment_advisor')

        logger.info('投资建议节点执行完成', { executionTime })

        return {
          ...updatedState,
          currentStep: 'investment_advisor_complete',
          lastNodeExecuted: 'investment_advisor',
          executionPath: [...(state.executionPath || []), 'investment_advisor'],
          nodeResults: {
            ...state.nodeResults,
            investment_advisor: {
              success: true,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      } catch (error) {
        const executionTime = Date.now() - startTime
        const errorMessage =
          error instanceof Error ? error.message : '投资建议节点执行失败'

        logger.error('投资建议节点执行失败:', error)

        return {
          error: errorMessage,
          currentStep: 'error',
          lastNodeExecuted: 'investment_advisor',
          executionPath: [...(state.executionPath || []), 'investment_advisor'],
          nodeResults: {
            ...state.nodeResults,
            investment_advisor: {
              success: false,
              error: errorMessage,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      }
    }
  }

  /**
   * 创建风险评估节点
   */
  private createRiskAssessmentNode() {
    return async (state: WorkflowState): Promise<Partial<WorkflowState>> => {
      const startTime = Date.now()

      try {
        logger.info('执行风险评估节点')

        const agent = this.agents.get('risk_assessment')
        if (!agent) {
          throw new Error('风险评估代理未找到')
        }

        const updatedState = await agent.execute(state)

        const executionTime = Date.now() - startTime
        this.executionContext.nodeExecutionTimes['risk_assessment'] = executionTime
        this.executionContext.visitedNodes.push('risk_assessment')

        logger.info('风险评估节点执行完成', { executionTime })

        return {
          ...updatedState,
          currentStep: 'risk_assessment_complete',
          lastNodeExecuted: 'risk_assessment',
          executionPath: [...(state.executionPath || []), 'risk_assessment'],
          nodeResults: {
            ...state.nodeResults,
            risk_assessment: {
              success: true,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      } catch (error) {
        const executionTime = Date.now() - startTime
        const errorMessage =
          error instanceof Error ? error.message : '风险评估节点执行失败'

        logger.error('风险评估节点执行失败:', error)

        return {
          error: errorMessage,
          currentStep: 'error',
          lastNodeExecuted: 'risk_assessment',
          executionPath: [...(state.executionPath || []), 'risk_assessment'],
          nodeResults: {
            ...state.nodeResults,
            risk_assessment: {
              success: false,
              error: errorMessage,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      }
    }
  }

  /**
   * 创建新闻分析节点
   */
  private createNewsAnalysisNode() {
    return async (state: WorkflowState): Promise<Partial<WorkflowState>> => {
      const startTime = Date.now()

      try {
        logger.info('执行新闻分析节点')

        const agent = this.agents.get('news_analysis')
        if (!agent) {
          throw new Error('新闻分析代理未找到')
        }

        const updatedState = await agent.execute(state)

        const executionTime = Date.now() - startTime
        this.executionContext.nodeExecutionTimes['news_analysis'] = executionTime
        this.executionContext.visitedNodes.push('news_analysis')

        logger.info('新闻分析节点执行完成', { executionTime })

        return {
          ...updatedState,
          currentStep: 'news_analysis_complete',
          lastNodeExecuted: 'news_analysis',
          executionPath: [...(state.executionPath || []), 'news_analysis'],
          nodeResults: {
            ...state.nodeResults,
            news_analysis: {
              success: true,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      } catch (error) {
        const executionTime = Date.now() - startTime
        const errorMessage =
          error instanceof Error ? error.message : '新闻分析节点执行失败'

        logger.error('新闻分析节点执行失败:', error)

        return {
          error: errorMessage,
          currentStep: 'error',
          lastNodeExecuted: 'news_analysis',
          executionPath: [...(state.executionPath || []), 'news_analysis'],
          nodeResults: {
            ...state.nodeResults,
            news_analysis: {
              success: false,
              error: errorMessage,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      }
    }
  }

  /**
   * 创建协调节点
   */
  private createCoordinatorNode() {
    return async (state: WorkflowState): Promise<Partial<WorkflowState>> => {
      const startTime = Date.now()

      try {
        logger.info('执行协调节点')

        const agent = this.agents.get('coordinator')
        if (!agent) {
          throw new Error('协调代理未找到')
        }

        const updatedState = await agent.execute(state)

        const executionTime = Date.now() - startTime
        this.executionContext.nodeExecutionTimes['coordinator'] = executionTime
        this.executionContext.visitedNodes.push('coordinator')

        logger.info('协调节点执行完成', { executionTime })

        return {
          ...updatedState,
          currentStep: 'coordinator_complete',
          isComplete: true,
          lastNodeExecuted: 'coordinator',
          executionPath: [...(state.executionPath || []), 'coordinator'],
          nodeResults: {
            ...state.nodeResults,
            coordinator: {
              success: true,
              executionTime,
              timestamp: new Date(),
            },
          },
          conditionalFlags: {
            ...state.conditionalFlags,
            workflowComplete: true,
          },
        }
      } catch (error) {
        const executionTime = Date.now() - startTime
        const errorMessage = error instanceof Error ? error.message : '协调节点执行失败'

        logger.error('协调节点执行失败:', error)

        return {
          error: errorMessage,
          currentStep: 'error',
          lastNodeExecuted: 'coordinator',
          executionPath: [...(state.executionPath || []), 'coordinator'],
          nodeResults: {
            ...state.nodeResults,
            coordinator: {
              success: false,
              error: errorMessage,
              executionTime,
              timestamp: new Date(),
            },
          },
        }
      }
    }
  }

  /**
   * 创建错误处理节点
   */
  private createErrorHandlerNode() {
    return async (state: WorkflowState): Promise<Partial<WorkflowState>> => {
      logger.info('执行错误处理节点')

      const retryCount = (state.retryCount || 0) + 1
      const maxRetries = 3

      if (
        retryCount <= maxRetries &&
        state.error &&
        !state.error.includes('不可恢复')
      ) {
        logger.info(`尝试重试，第 ${retryCount} 次`, { maxRetries })

        return {
          error: undefined,
          retryCount,
          currentStep: 'retry',
          conditionalFlags: {
            ...state.conditionalFlags,
            shouldRetry: true,
          },
        }
      }

      logger.error('工作流执行失败，已达到最大重试次数', {
        retryCount,
        maxRetries,
        error: state.error,
      })

      return {
        currentStep: 'failed',
        isComplete: true,
        conditionalFlags: {
          ...state.conditionalFlags,
          workflowFailed: true,
        },
      }
    }
  }

  /**
   * 创建路由条件函数
   */
  private createRoutingCondition() {
    return (state: WorkflowState): string => {
      if (state.error) {
        return 'error'
      }

      if (state.financialAnalysis) {
        return 'continue'
      }

      return 'error'
    }
  }

  /**
   * 创建完成条件函数
   */
  private createCompletionCondition() {
    return (state: WorkflowState): string => {
      if (state.error) {
        return 'error'
      }

      if (state.conditionalFlags?.shouldRetry) {
        return 'retry'
      }

      if (state.isComplete) {
        return 'complete'
      }

      return 'error'
    }
  }

  /**
   * 执行工作流
   */
  async execute(
    initialState: Partial<WorkflowState>
  ): Promise<WorkflowExecutionResult> {
    const startTime = Date.now()
    const executionId = `exec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    this.executionContext = {
      ...this.executionContext,
      executionId,
      startTime: new Date(),
      visitedNodes: [],
      nodeExecutionTimes: {},
      errors: [],
      checkpoints: [],
    }

    try {
      logger.info('开始执行StateGraph工作流', { executionId })

      // 执行StateGraph
      const finalState = await this.compiledGraph.invoke(initialState)

      const executionTime = Date.now() - startTime

      logger.info('StateGraph工作流执行完成', {
        executionId,
        executionTime,
        success: !finalState.error,
        visitedNodes: this.executionContext.visitedNodes,
      })

      // 构建执行步骤
      const steps = this.executionContext.visitedNodes.map((nodeId, index) => ({
        stepId: `${nodeId}_${index}`,
        agentName: nodeId,
        startTime: new Date(startTime + index * 1000), // 估算时间
        endTime: new Date(startTime + (index + 1) * 1000),
        input: initialState,
        output: finalState,
        success: finalState.nodeResults?.[nodeId]?.success || false,
        error: finalState.nodeResults?.[nodeId]?.error,
      }))

      return {
        success: !finalState.error && finalState.isComplete,
        state: finalState,
        executionTime,
        steps,
        error: finalState.error,
        executionContext: this.executionContext,
      }
    } catch (error) {
      const executionTime = Date.now() - startTime
      const errorMessage =
        error instanceof Error ? error.message : 'StateGraph工作流执行失败'

      logger.error('StateGraph工作流执行失败:', error)

      return {
        success: false,
        state: {
          ...initialState,
          error: errorMessage,
          currentStep: 'error',
          isComplete: true,
        } as WorkflowState,
        executionTime,
        steps: [],
        error: errorMessage,
        executionContext: this.executionContext,
      }
    }
  }

  /**
   * 获取工作流状态
   */
  getWorkflowState(): StateGraphWorkflowInstance | null {
    return this.workflowInstance
  }

  /**
   * 暂停工作流
   */
  async pauseWorkflow(): Promise<boolean> {
    try {
      // StateGraph暂停逻辑
      logger.info('暂停工作流')
      return true
    } catch (error) {
      logger.error('暂停工作流失败:', error)
      return false
    }
  }

  /**
   * 恢复工作流
   */
  async resumeWorkflow(): Promise<boolean> {
    try {
      // StateGraph恢复逻辑
      logger.info('恢复工作流')
      return true
    } catch (error) {
      logger.error('恢复工作流失败:', error)
      return false
    }
  }

  /**
   * 获取工作流图的可视化表示
   */
  getGraphVisualization(): any {
    try {
      // 返回图的结构信息用于可视化
      return {
        nodes: [
          'financial_analysis',
          'investment_advisor',
          'risk_assessment',
          'news_analysis',
          'coordinator',
          'error_handler',
        ],
        edges: [
          { from: 'START', to: 'financial_analysis' },
          { from: 'financial_analysis', to: 'investment_advisor' },
          { from: 'investment_advisor', to: 'risk_assessment' },
          { from: 'risk_assessment', to: 'news_analysis' },
          { from: 'news_analysis', to: 'coordinator' },
          { from: 'coordinator', to: 'END' },
          { from: 'error_handler', to: 'END' },
        ],
        executionPath: this.executionContext.visitedNodes,
        nodeExecutionTimes: this.executionContext.nodeExecutionTimes,
      }
    } catch (error) {
      logger.error('获取图可视化失败:', error)
      return null
    }
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      // 检查所有代理
      for (const [name, agent] of this.agents.entries()) {
        if (!agent || typeof agent.execute !== 'function') {
          logger.error(`代理 ${name} 不健康`)
          return false
        }
      }

      // 检查StateGraph
      if (!this.compiledGraph) {
        logger.error('StateGraph未编译')
        return false
      }

      logger.info('StateGraph工作流健康检查通过')
      return true
    } catch (error) {
      logger.error('StateGraph工作流健康检查失败:', error)
      return false
    }
  }

  /**
   * 获取代理信息
   */
  getAgentInfo(): Array<{ name: string; description: string }> {
    return Array.from(this.agents.entries()).map(([name, agent]) => ({
      name,
      description: agent.getDescription ? agent.getDescription() : `${name} agent`,
    }))
  }

  /**
   * 重置工作流
   */
  reset(): void {
    this.executionContext = {
      graphId: 'financial_workflow',
      executionId: '',
      startTime: new Date(),
      visitedNodes: [],
      nodeExecutionTimes: {},
      errors: [],
      checkpoints: [],
    }
    this.workflowInstance = null
  }

  /**
   * 获取执行步骤
   */
  getExecutionSteps() {
    return this.executionContext.visitedNodes.map((nodeId, index) => ({
      stepId: `${nodeId}_${index}`,
      agentName: nodeId,
      startTime: new Date(),
      endTime: new Date(),
      input: {},
      output: {},
      success: true,
    }))
  }
}

// 创建默认工作流实例
export const createFinancialStateGraphWorkflow = (
  modelId?: string
): FinancialWorkflowStateGraph => {
  return new FinancialWorkflowStateGraph(modelId)
}

// 导出工作流配置
export const STATEGRAPH_WORKFLOW_CONFIG = {
  name: 'FinancialAnalysisStateGraphWorkflow',
  description: '基于LangGraph StateGraph的综合财务分析工作流',
  version: '2.0.0',
  nodes: [
    'financial_analysis',
    'investment_advisor',
    'risk_assessment',
    'news_analysis',
    'coordinator',
    'error_handler',
  ],
  maxExecutionTime: 300000, // 5分钟
  maxRetries: 3,
  enableParallelExecution: false, // 暂时使用顺序执行
  enableConditionalRouting: true,
  enableErrorRecovery: true,
  enableStateCheckpoints: true,
}

// 为了向后兼容，保留旧的导出
export const createFinancialWorkflow = createFinancialStateGraphWorkflow
export const FinancialWorkflowGraph = FinancialWorkflowStateGraph
export const WORKFLOW_CONFIG = STATEGRAPH_WORKFLOW_CONFIG
