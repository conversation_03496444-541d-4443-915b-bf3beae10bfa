import React from 'react'
import { Outlet } from 'react-router-dom'
import { Layout as AntLayout } from 'antd'
import { Header } from './Header'
import { Sidebar } from './Sidebar'
import { useAppStore } from '../../stores/appStore'

const { Content } = AntLayout

export const Layout: React.FC = () => {
  const { sidebarCollapsed } = useAppStore()

  return (
    <AntLayout className="min-h-screen">
      <Sidebar />
      <AntLayout
        className={`transition-all duration-300 ${
          sidebarCollapsed ? 'ml-20' : 'ml-64'
        }`}
      >
        <Header />
        <Content className="p-6 bg-gray-50">
          <div className="max-w-7xl mx-auto">
            <Outlet />
          </div>
        </Content>
      </AntLayout>
    </AntLayout>
  )
}
