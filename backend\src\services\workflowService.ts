import { logger } from '../utils/logger'
import { createFinancialWorkflow, WORKFLOW_CONFIG } from '../agents/workflowGraph'
import type {
  WorkflowState,
  WorkflowExecutionResult,
  ExecutionStep,
} from '../types/agents'
import { BaseMessage, HumanMessage } from '@langchain/core/messages'

export class WorkflowService {
  private workflows: Map<string, any> = new Map()

  constructor() {
    // 初始化默认工作流
    this.initializeWorkflows()
  }

  /**
   * 初始化工作流
   */
  private initializeWorkflows(): void {
    try {
      // 创建财务分析工作流
      const financialWorkflow = createFinancialWorkflow()
      this.workflows.set('financial_analysis', financialWorkflow)

      logger.info('工作流服务初始化完成')
    } catch (error) {
      logger.error('工作流服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 执行完整的财务分析工作流
   */
  async executeFinancialWorkflow(params: {
    userId: string
    conversationId: string
    userQuery: string
    modelId?: string
  }): Promise<WorkflowExecutionResult> {
    try {
      logger.info('开始执行财务分析工作流', {
        userId: params.userId,
        conversationId: params.conversationId,
      })

      const workflow = this.workflows.get('financial_analysis')
      if (!workflow) {
        throw new Error('财务分析工作流不存在')
      }

      // 构建初始状态
      const initialState: Partial<WorkflowState> = {
        userId: params.userId,
        conversationId: params.conversationId,
        userQuery: params.userQuery,
        modelId: params.modelId || 'deepseek-chat',
        messages: [new HumanMessage(params.userQuery)],
        currentStep: 'start',
        isComplete: false,
        context: {
          startTime: new Date(),
          workflowType: 'financial_analysis',
        },
      }

      // 执行工作流
      const result = await workflow.execute(initialState)

      logger.info('财务分析工作流执行完成', {
        success: result.success,
        executionTime: result.executionTime,
        stepsCount: result.steps.length,
      })

      return result
    } catch (error) {
      logger.error('执行财务分析工作流失败:', error)
      throw error
    }
  }

  /**
   * 执行单个代理
   */
  async executeSingleAgent(params: {
    agentName: string
    userId: string
    conversationId: string
    userQuery: string
    modelId?: string
    context?: Record<string, any>
  }): Promise<WorkflowExecutionResult> {
    try {
      logger.info(`开始执行单个代理: ${params.agentName}`)

      const workflow = this.workflows.get('financial_analysis')
      if (!workflow) {
        throw new Error('工作流不存在')
      }

      // 构建初始状态
      const initialState: Partial<WorkflowState> = {
        userId: params.userId,
        conversationId: params.conversationId,
        userQuery: params.userQuery,
        modelId: params.modelId || 'deepseek-chat',
        messages: [new HumanMessage(params.userQuery)],
        currentStep: 'start',
        isComplete: false,
        context: {
          startTime: new Date(),
          agentType: params.agentName,
          ...params.context,
        },
      }

      // 执行单个代理
      const result = await workflow.executeSingleAgent(params.agentName, initialState)

      logger.info(`单个代理 ${params.agentName} 执行完成`, {
        success: result.success,
        executionTime: result.executionTime,
      })

      return result
    } catch (error) {
      logger.error(`执行单个代理 ${params.agentName} 失败:`, error)
      throw error
    }
  }

  /**
   * 并行执行多个代理
   */
  async executeParallelAgents(params: {
    agentNames: string[]
    userId: string
    conversationId: string
    userQuery: string
    modelId?: string
    context?: Record<string, any>
  }): Promise<WorkflowExecutionResult> {
    try {
      logger.info(`开始并行执行代理: ${params.agentNames.join(', ')}`)

      const workflow = this.workflows.get('financial_analysis')
      if (!workflow) {
        throw new Error('工作流不存在')
      }

      // 构建初始状态
      const initialState: Partial<WorkflowState> = {
        userId: params.userId,
        conversationId: params.conversationId,
        userQuery: params.userQuery,
        modelId: params.modelId || 'deepseek-chat',
        messages: [new HumanMessage(params.userQuery)],
        currentStep: 'start',
        isComplete: false,
        context: {
          startTime: new Date(),
          executionType: 'parallel',
          agentNames: params.agentNames,
          ...params.context,
        },
      }

      // 并行执行代理
      const result = await workflow.executeParallel(initialState, params.agentNames)

      logger.info(`并行代理执行完成`, {
        success: result.success,
        executionTime: result.executionTime,
        agentsCount: params.agentNames.length,
      })

      return result
    } catch (error) {
      logger.error(`并行执行代理失败:`, error)
      throw error
    }
  }

  /**
   * 获取工作流状态
   */
  async getWorkflowStatus(workflowType: string = 'financial_analysis'): Promise<{
    isHealthy: boolean
    agentInfo: Array<{ name: string; description: string }>
    config: typeof WORKFLOW_CONFIG
  }> {
    try {
      const workflow = this.workflows.get(workflowType)
      if (!workflow) {
        throw new Error(`工作流 ${workflowType} 不存在`)
      }

      const isHealthy = await workflow.healthCheck()
      const agentInfo = workflow.getAgentInfo()

      return {
        isHealthy,
        agentInfo,
        config: WORKFLOW_CONFIG,
      }
    } catch (error) {
      logger.error('获取工作流状态失败:', error)
      return {
        isHealthy: false,
        agentInfo: [],
        config: WORKFLOW_CONFIG,
      }
    }
  }

  /**
   * 重置工作流
   */
  resetWorkflow(workflowType: string = 'financial_analysis'): void {
    try {
      const workflow = this.workflows.get(workflowType)
      if (workflow) {
        workflow.reset()
        logger.info(`工作流 ${workflowType} 已重置`)
      }
    } catch (error) {
      logger.error(`重置工作流 ${workflowType} 失败:`, error)
    }
  }

  /**
   * 获取执行历史
   */
  getExecutionHistory(workflowType: string = 'financial_analysis'): ExecutionStep[] {
    try {
      const workflow = this.workflows.get(workflowType)
      if (!workflow) {
        return []
      }

      return workflow.getExecutionSteps()
    } catch (error) {
      logger.error('获取执行历史失败:', error)
      return []
    }
  }

  /**
   * 创建自定义工作流
   */
  createCustomWorkflow(params: {
    name: string
    agentNames: string[]
    modelId?: string
  }): boolean {
    try {
      const workflow = createFinancialWorkflow(params.modelId)
      this.workflows.set(params.name, workflow)

      logger.info(`自定义工作流 ${params.name} 创建成功`)
      return true
    } catch (error) {
      logger.error(`创建自定义工作流 ${params.name} 失败:`, error)
      return false
    }
  }

  /**
   * 删除工作流
   */
  deleteWorkflow(workflowType: string): boolean {
    try {
      if (workflowType === 'financial_analysis') {
        logger.warn('不能删除默认的财务分析工作流')
        return false
      }

      const deleted = this.workflows.delete(workflowType)
      if (deleted) {
        logger.info(`工作流 ${workflowType} 已删除`)
      }

      return deleted
    } catch (error) {
      logger.error(`删除工作流 ${workflowType} 失败:`, error)
      return false
    }
  }

  /**
   * 获取所有工作流列表
   */
  getWorkflowList(): string[] {
    return Array.from(this.workflows.keys())
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      // 检查所有工作流的健康状态
      for (const [name, workflow] of this.workflows.entries()) {
        const isHealthy = await workflow.healthCheck()
        if (!isHealthy) {
          logger.error(`工作流 ${name} 健康检查失败`)
          return false
        }
      }

      logger.info('工作流服务健康检查通过')
      return true
    } catch (error) {
      logger.error('工作流服务健康检查失败:', error)
      return false
    }
  }
}

// 创建单例实例
export const workflowService = new WorkflowService()
