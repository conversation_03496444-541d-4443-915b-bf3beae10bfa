import { Router } from 'express'
import { debtController } from '../controllers/debtController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 获取负债类型（无需认证）
router.get('/types', asyncHandler(debtController.getDebtTypes))

// 以下路由需要认证
router.use(authMiddleware)

// 获取负债统计
router.get('/stats', asyncHandler(debtController.getDebtStats))

// 获取用户所有负债
router.get('/', asyncHandler(debtController.getUserDebts))

// 创建新负债
router.post('/', asyncHandler(debtController.createDebt))

// 获取单个负债详情
router.get('/:debtId', asyncHandler(debtController.getDebtById))

// 更新负债
router.put('/:debtId', asyncHandler(debtController.updateDebt))

// 删除负债
router.delete('/:debtId', async<PERSON>and<PERSON>(debtController.deleteDebt))

// 记录还款
router.post('/:debtId/payments', asyncHandler(debtController.recordPayment))

// 获取还款历史
router.get('/:debtId/payments', asyncHandler(debtController.getPaymentHistory))

export default router
