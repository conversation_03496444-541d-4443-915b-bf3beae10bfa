import React, { useState, useEffect } from 'react'
import {
  Typo<PERSON>,
  Card,
  Table,
  Button,
  Space,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  message,
  Popconfirm,
  Tag,
  Statistic,
  Row,
  Col,
  Tooltip,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  RiseOutlined,
  FallOutlined,
  DollarOutlined,
} from '@ant-design/icons'
import { assetService } from '../../services/assetService'
import type { Asset, AssetCategory } from '../../types'

const { Title } = Typography
const { Option } = Select

interface AssetFormData {
  name: string
  categoryId: string
  currentValue: number
  purchasePrice?: number
  purchaseDate?: string
  description?: string
}

export const AssetsPage: React.FC = () => {
  const [assets, setAssets] = useState<Asset[]>([])
  const [categories, setCategories] = useState<AssetCategory[]>([])
  const [loading, setLoading] = useState(false)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingAsset, setEditingAsset] = useState<Asset | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('')
  const [statistics, setStatistics] = useState<any>(null)
  const [form] = Form.useForm()

  // 加载数据
  useEffect(() => {
    loadAssets()
    loadCategories()
    loadStatistics()
  }, [])

  const loadAssets = async () => {
    try {
      setLoading(true)
      const response = await assetService.getAssets()
      if (response.success && response.data) {
        setAssets(response.data)
      }
    } catch (error) {
      message.error('加载资产失败')
    } finally {
      setLoading(false)
    }
  }

  const loadCategories = async () => {
    try {
      const response = await assetService.getAssetCategories()
      if (response.success && response.data) {
        setCategories(response.data)
      }
    } catch (error) {
      message.error('加载分类失败')
    }
  }

  const loadStatistics = async () => {
    try {
      const response = await assetService.getAssetStatistics()
      if (response.success && response.data) {
        setStatistics(response.data)
      }
    } catch (error) {
      console.error('加载统计数据失败:', error)
    }
  }

  // 处理搜索
  const handleSearch = async (value: string) => {
    if (!value.trim()) {
      loadAssets()
      return
    }

    try {
      setLoading(true)
      const response = await assetService.searchAssets(value)
      if (response.success && response.data) {
        setAssets(response.data)
      }
    } catch (error) {
      message.error('搜索失败')
    } finally {
      setLoading(false)
    }
  }

  // 按分类筛选
  const handleCategoryFilter = async (categoryId: string) => {
    setSelectedCategory(categoryId)

    if (!categoryId) {
      loadAssets()
      return
    }

    try {
      setLoading(true)
      const response = await assetService.getAssetsByCategory(categoryId)
      if (response.success && response.data) {
        setAssets(response.data)
      }
    } catch (error) {
      message.error('筛选失败')
    } finally {
      setLoading(false)
    }
  }

  // 打开新增/编辑模态框
  const openModal = (asset?: Asset) => {
    setEditingAsset(asset || null)
    setModalVisible(true)

    if (asset) {
      form.setFieldsValue({
        ...asset,
        purchaseDate: asset.purchaseDate ? new Date(asset.purchaseDate) : undefined,
      })
    } else {
      form.resetFields()
    }
  }

  // 关闭模态框
  const closeModal = () => {
    setModalVisible(false)
    setEditingAsset(null)
    form.resetFields()
  }

  // 保存资产
  const handleSave = async (values: AssetFormData) => {
    try {
      const data = {
        ...values,
        purchaseDate: values.purchaseDate
          ? new Date(values.purchaseDate).toISOString().split('T')[0]
          : undefined,
      }

      if (editingAsset) {
        const response = await assetService.updateAsset(editingAsset.id, data)
        if (response.success) {
          message.success('资产更新成功')
          loadAssets()
          loadStatistics()
          closeModal()
        }
      } else {
        const response = await assetService.createAsset(data as any)
        if (response.success) {
          message.success('资产创建成功')
          loadAssets()
          loadStatistics()
          closeModal()
        }
      }
    } catch (error) {
      message.error(editingAsset ? '更新失败' : '创建失败')
    }
  }

  // 删除资产
  const handleDelete = async (id: string) => {
    try {
      const response = await assetService.deleteAsset(id)
      if (response.success) {
        message.success('资产删除成功')
        loadAssets()
        loadStatistics()
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 计算收益
  const calculateGain = (asset: Asset) => {
    if (!asset.purchasePrice) return null
    const gain = asset.currentValue - asset.purchasePrice
    const gainPercent = (gain / asset.purchasePrice) * 100
    return { gain, gainPercent }
  }

  // 表格列定义
  const columns = [
    {
      title: '资产名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Asset) => (
        <div>
          <div className="font-medium">{text}</div>
          {(record as any).description && (
            <div className="text-sm text-gray-500">{(record as any).description}</div>
          )}
        </div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryId',
      key: 'categoryId',
      render: (categoryId: string) => {
        const category = categories.find((c) => c.id === categoryId)
        return category ? (
          <Tag color={(category as any).color || 'blue'}>{category.name}</Tag>
        ) : (
          categoryId
        )
      },
    },
    {
      title: '当前价值',
      dataIndex: 'currentValue',
      key: 'currentValue',
      render: (value: number, record: Asset) => (
        <div className="text-right">
          <div className="font-medium">¥{value.toLocaleString()}</div>
          <div className="text-sm text-gray-500">
            {(record as any).currency || 'CNY'}
          </div>
        </div>
      ),
      sorter: (a: Asset, b: Asset) => a.currentValue - b.currentValue,
    },
    {
      title: '购买价格',
      dataIndex: 'purchasePrice',
      key: 'purchasePrice',
      render: (price: number) => (price ? `¥${price.toLocaleString()}` : '-'),
    },
    {
      title: '收益',
      key: 'gain',
      render: (_: any, record: Asset) => {
        const gain = calculateGain(record)
        if (!gain) return '-'

        const isPositive = gain.gain >= 0
        return (
          <div
            className={`text-right ${isPositive ? 'text-green-600' : 'text-red-600'}`}
          >
            <div className="flex items-center justify-end">
              {isPositive ? <RiseOutlined /> : <FallOutlined />}
              <span className="ml-1">¥{Math.abs(gain.gain).toLocaleString()}</span>
            </div>
            <div className="text-sm">
              {isPositive ? '+' : ''}
              {gain.gainPercent.toFixed(2)}%
            </div>
          </div>
        )
      },
      sorter: (a: Asset, b: Asset) => {
        const gainA = calculateGain(a)
        const gainB = calculateGain(b)
        if (!gainA && !gainB) return 0
        if (!gainA) return -1
        if (!gainB) return 1
        return gainA.gain - gainB.gain
      },
    },
    {
      title: '购买日期',
      dataIndex: 'purchaseDate',
      key: 'purchaseDate',
      render: (date: string) => (date ? new Date(date).toLocaleDateString() : '-'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Asset) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => openModal(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个资产吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button type="text" danger icon={<DeleteOutlined />} />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex justify-between items-center">
        <Title level={2} className="mb-0">
          资产管理
        </Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => openModal()}>
          添加资产
        </Button>
      </div>

      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={16}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总资产价值"
                value={statistics.totalValue}
                precision={2}
                prefix={<DollarOutlined />}
                suffix="¥"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="资产数量" value={statistics.totalAssets} suffix="个" />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总收益"
                value={statistics.totalGain || 0}
                precision={2}
                prefix={statistics.totalGain >= 0 ? <RiseOutlined /> : <FallOutlined />}
                suffix="¥"
                valueStyle={{
                  color: statistics.totalGain >= 0 ? '#3f8600' : '#cf1322',
                }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="收益率"
                value={statistics.totalGainPercentage || 0}
                precision={2}
                suffix="%"
                valueStyle={{
                  color: statistics.totalGainPercentage >= 0 ? '#3f8600' : '#cf1322',
                }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 搜索和筛选 */}
      <Card>
        <div className="flex justify-between items-center mb-4">
          <Space>
            <Input.Search
              placeholder="搜索资产名称或描述"
              allowClear
              style={{ width: 300 }}
              onSearch={handleSearch}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <Select
              placeholder="选择分类"
              allowClear
              style={{ width: 150 }}
              value={selectedCategory}
              onChange={handleCategoryFilter}
            >
              {categories.map((category) => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Space>
        </div>

        {/* 资产表格 */}
        <Table
          columns={columns}
          dataSource={assets}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 项资产`,
          }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingAsset ? '编辑资产' : '添加资产'}
        open={modalVisible}
        onCancel={closeModal}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSave}>
          <Form.Item
            name="name"
            label="资产名称"
            rules={[{ required: true, message: '请输入资产名称' }]}
          >
            <Input placeholder="请输入资产名称" />
          </Form.Item>

          <Form.Item
            name="categoryId"
            label="资产分类"
            rules={[{ required: true, message: '请选择资产分类' }]}
          >
            <Select placeholder="请选择资产分类">
              {categories.map((category) => (
                <Option key={category.id} value={category.id}>
                  {category.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="currentValue"
                label="当前价值"
                rules={[{ required: true, message: '请输入当前价值' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入当前价值"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="purchasePrice" label="购买价格">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入购买价格"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="purchaseDate" label="购买日期">
            <DatePicker style={{ width: '100%' }} placeholder="请选择购买日期" />
          </Form.Item>

          <Form.Item name="description" label="描述">
            <Input.TextArea rows={3} placeholder="请输入资产描述（可选）" />
          </Form.Item>

          <Form.Item className="mb-0">
            <div className="flex justify-end space-x-2">
              <Button onClick={closeModal}>取消</Button>
              <Button type="primary" htmlType="submit">
                {editingAsset ? '更新' : '创建'}
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}
