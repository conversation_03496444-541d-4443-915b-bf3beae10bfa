# AI 驱动资产管理应用 - 数据库设计

## 数据库概览

使用 SQLite 作为主数据库，配合 Drizzle ORM 进行类型安全的数据访问。

## 实体关系图 (ERD)

```mermaid
erDiagram
    Users ||--o{ Assets : owns
    Users ||--o{ Debts : has
    Users ||--o{ AIConversations : creates
    Users ||--o{ CalendarEvents : schedules
    Users ||--o{ UserSettings : configures

    Assets ||--o{ AssetHistory : tracks
    Assets }o--|| AssetCategories : belongs_to

    Debts ||--o{ DebtPayments : has
    Debts }o--|| DebtTypes : belongs_to

    AIConversations ||--o{ AIMessages : contains
    AIMessages }o--|| AIModels : uses

    News ||--o{ NewsCategories : categorized_by

    MarketData }o--|| MarketIndicators : belongs_to

    Users {
        string id PK
        string email UK
        string password_hash
        string name
        string avatar_url
        timestamp created_at
        timestamp updated_at
        boolean is_active
    }

    UserSettings {
        string id PK
        string user_id FK
        json ai_api_keys
        string preferred_currency
        string timezone
        json notification_settings
        json dashboard_layout
        timestamp updated_at
    }

    Assets {
        string id PK
        string user_id FK
        string category_id FK
        string name
        string description
        decimal current_value
        decimal purchase_price
        date purchase_date
        string currency
        json metadata
        timestamp created_at
        timestamp updated_at
    }

    AssetCategories {
        string id PK
        string name
        string description
        string icon
        string color
        boolean is_system
    }

    AssetHistory {
        string id PK
        string asset_id FK
        decimal value
        date record_date
        string source
        json metadata
        timestamp created_at
    }

    Debts {
        string id PK
        string user_id FK
        string type_id FK
        string name
        string description
        decimal principal_amount
        decimal current_balance
        decimal interest_rate
        date start_date
        date due_date
        integer payment_frequency
        decimal monthly_payment
        string currency
        json metadata
        timestamp created_at
        timestamp updated_at
    }

    DebtTypes {
        string id PK
        string name
        string description
        string icon
        string color
        boolean is_system
    }

    DebtPayments {
        string id PK
        string debt_id FK
        decimal amount
        decimal principal_amount
        decimal interest_amount
        date payment_date
        string status
        json metadata
        timestamp created_at
    }

    AIConversations {
        string id PK
        string user_id FK
        string title
        string context_type
        json context_data
        timestamp created_at
        timestamp updated_at
    }

    AIMessages {
        string id PK
        string conversation_id FK
        string model_id FK
        string role
        text content
        json metadata
        decimal tokens_used
        decimal cost
        timestamp created_at
    }

    AIModels {
        string id PK
        string name
        string provider
        string model_id
        decimal cost_per_token
        json capabilities
        boolean is_active
        timestamp created_at
    }

    News {
        string id PK
        string title
        text content
        text summary
        string url
        string source
        string author
        date published_date
        json categories
        decimal relevance_score
        json metadata
        timestamp created_at
        timestamp updated_at
    }

    NewsCategories {
        string id PK
        string name
        string description
        string keywords
        boolean is_active
    }

    MarketData {
        string id PK
        string indicator_id FK
        decimal value
        decimal change_amount
        decimal change_percentage
        date record_date
        string source
        json metadata
        timestamp created_at
    }

    MarketIndicators {
        string id PK
        string name
        string symbol
        string description
        string category
        string source_api
        boolean is_active
        timestamp created_at
    }

    CalendarEvents {
        string id PK
        string user_id FK
        string title
        text description
        string event_type
        datetime start_time
        datetime end_time
        boolean is_recurring
        json recurrence_rule
        json metadata
        timestamp created_at
        timestamp updated_at
    }
```

## 详细表结构设计

### 1. 用户管理模块

#### Users 表

```sql
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    name TEXT NOT NULL,
    avatar_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_created_at ON users(created_at);
```

#### UserSettings 表

```sql
CREATE TABLE user_settings (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    ai_api_keys TEXT, -- JSON格式存储加密的API Keys
    preferred_currency TEXT DEFAULT 'USD',
    timezone TEXT DEFAULT 'UTC',
    notification_settings TEXT, -- JSON格式
    dashboard_layout TEXT, -- JSON格式存储布局配置
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_user_settings_user_id ON user_settings(user_id);
```

### 2. 资产管理模块

#### AssetCategories 表

```sql
CREATE TABLE asset_categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    is_system BOOLEAN DEFAULT FALSE
);

-- 预设资产类别
INSERT INTO asset_categories VALUES
('cash', '现金', '现金和现金等价物', 'cash', '#4CAF50', TRUE),
('stocks', '股票', '股票投资', 'trending-up', '#2196F3', TRUE),
('bonds', '债券', '债券投资', 'shield', '#FF9800', TRUE),
('real_estate', '房产', '房地产投资', 'home', '#9C27B0', TRUE),
('vehicles', '车辆', '汽车等交通工具', 'car', '#607D8B', TRUE),
('crypto', '加密货币', '数字货币投资', 'bitcoin', '#FFC107', TRUE);
```

#### Assets 表

```sql
CREATE TABLE assets (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    category_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    current_value DECIMAL(15,2) NOT NULL,
    purchase_price DECIMAL(15,2),
    purchase_date DATE,
    currency TEXT DEFAULT 'USD',
    metadata TEXT, -- JSON格式存储额外信息
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES asset_categories(id)
);

CREATE INDEX idx_assets_user_id ON assets(user_id);
CREATE INDEX idx_assets_category_id ON assets(category_id);
CREATE INDEX idx_assets_created_at ON assets(created_at);
```

#### AssetHistory 表

```sql
CREATE TABLE asset_history (
    id TEXT PRIMARY KEY,
    asset_id TEXT NOT NULL,
    value DECIMAL(15,2) NOT NULL,
    record_date DATE NOT NULL,
    source TEXT DEFAULT 'manual',
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (asset_id) REFERENCES assets(id) ON DELETE CASCADE
);

CREATE INDEX idx_asset_history_asset_id ON asset_history(asset_id);
CREATE INDEX idx_asset_history_record_date ON asset_history(record_date);
```

### 3. 负债管理模块

#### DebtTypes 表

```sql
CREATE TABLE debt_types (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    is_system BOOLEAN DEFAULT FALSE
);

-- 预设负债类型
INSERT INTO debt_types VALUES
('credit_card', '信用卡', '信用卡债务', 'credit-card', '#F44336', TRUE),
('mortgage', '房贷', '房屋抵押贷款', 'home', '#3F51B5', TRUE),
('auto_loan', '车贷', '汽车贷款', 'car', '#009688', TRUE),
('personal_loan', '个人贷款', '个人消费贷款', 'user', '#795548', TRUE),
('student_loan', '学生贷款', '教育贷款', 'book', '#E91E63', TRUE);
```

#### Debts 表

```sql
CREATE TABLE debts (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    type_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    principal_amount DECIMAL(15,2) NOT NULL,
    current_balance DECIMAL(15,2) NOT NULL,
    interest_rate DECIMAL(5,4) NOT NULL,
    start_date DATE NOT NULL,
    due_date DATE,
    payment_frequency INTEGER DEFAULT 12, -- 每年还款次数
    monthly_payment DECIMAL(15,2),
    currency TEXT DEFAULT 'USD',
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (type_id) REFERENCES debt_types(id)
);

CREATE INDEX idx_debts_user_id ON debts(user_id);
CREATE INDEX idx_debts_type_id ON debts(type_id);
CREATE INDEX idx_debts_due_date ON debts(due_date);
```

#### DebtPayments 表

```sql
CREATE TABLE debt_payments (
    id TEXT PRIMARY KEY,
    debt_id TEXT NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    principal_amount DECIMAL(15,2) NOT NULL,
    interest_amount DECIMAL(15,2) NOT NULL,
    payment_date DATE NOT NULL,
    status TEXT DEFAULT 'completed',
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (debt_id) REFERENCES debts(id) ON DELETE CASCADE
);

CREATE INDEX idx_debt_payments_debt_id ON debt_payments(debt_id);
CREATE INDEX idx_debt_payments_payment_date ON debt_payments(payment_date);
```

### 4. AI 对话模块

#### AIModels 表

```sql
CREATE TABLE ai_models (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    provider TEXT NOT NULL,
    model_id TEXT NOT NULL,
    cost_per_token DECIMAL(10,8),
    capabilities TEXT, -- JSON格式
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 预设AI模型
INSERT INTO ai_models VALUES
('deepseek-chat', 'DeepSeek Chat', 'deepseek', 'deepseek-chat', 0.00000014, '{"chat": true, "function_calling": true}', TRUE, CURRENT_TIMESTAMP),
('gpt-4', 'GPT-4', 'openai', 'gpt-4', 0.00003, '{"chat": true, "function_calling": true, "vision": true}', TRUE, CURRENT_TIMESTAMP),
('claude-3-sonnet', 'Claude 3 Sonnet', 'anthropic', 'claude-3-sonnet-20240229', 0.000015, '{"chat": true, "function_calling": true}', TRUE, CURRENT_TIMESTAMP);
```

#### AIConversations 表

```sql
CREATE TABLE ai_conversations (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    context_type TEXT, -- 'asset_analysis', 'investment_advice', 'general'
    context_data TEXT, -- JSON格式存储相关上下文
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_ai_conversations_user_id ON ai_conversations(user_id);
CREATE INDEX idx_ai_conversations_created_at ON ai_conversations(created_at);
```

#### AIMessages 表

```sql
CREATE TABLE ai_messages (
    id TEXT PRIMARY KEY,
    conversation_id TEXT NOT NULL,
    model_id TEXT NOT NULL,
    role TEXT NOT NULL, -- 'user', 'assistant', 'system'
    content TEXT NOT NULL,
    metadata TEXT, -- JSON格式存储函数调用等信息
    tokens_used INTEGER,
    cost DECIMAL(10,8),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (conversation_id) REFERENCES ai_conversations(id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(id)
);

CREATE INDEX idx_ai_messages_conversation_id ON ai_messages(conversation_id);
CREATE INDEX idx_ai_messages_created_at ON ai_messages(created_at);
```

### 5. 新闻和市场数据模块

#### NewsCategories 表

```sql
CREATE TABLE news_categories (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    description TEXT,
    keywords TEXT, -- 逗号分隔的关键词
    is_active BOOLEAN DEFAULT TRUE
);

-- 预设新闻分类
INSERT INTO news_categories VALUES
('finance', '金融', '金融市场相关新闻', '股市,债券,基金,银行', TRUE),
('economy', '经济', '宏观经济新闻', 'GDP,通胀,利率,就业', TRUE),
('crypto', '加密货币', '数字货币相关', '比特币,以太坊,区块链', TRUE),
('real_estate', '房地产', '房地产市场', '房价,楼市,地产', TRUE);
```

#### News 表

```sql
CREATE TABLE news (
    id TEXT PRIMARY KEY,
    title TEXT NOT NULL,
    content TEXT,
    summary TEXT,
    url TEXT,
    source TEXT,
    author TEXT,
    published_date DATE,
    categories TEXT, -- JSON数组格式
    relevance_score DECIMAL(3,2), -- 0-1之间的相关性评分
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_news_published_date ON news(published_date);
CREATE INDEX idx_news_relevance_score ON news(relevance_score);
CREATE INDEX idx_news_source ON news(source);
```

#### MarketIndicators 表

```sql
CREATE TABLE market_indicators (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    symbol TEXT NOT NULL,
    description TEXT,
    category TEXT, -- 'index', 'commodity', 'currency'
    source_api TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 预设市场指标
INSERT INTO market_indicators VALUES
('sp500', 'S&P 500', 'SPX', '标普500指数', 'index', 'alpha_vantage', TRUE, CURRENT_TIMESTAMP),
('nasdaq', 'NASDAQ', 'IXIC', '纳斯达克综合指数', 'index', 'alpha_vantage', TRUE, CURRENT_TIMESTAMP),
('gold', '黄金', 'GOLD', '黄金现货价格', 'commodity', 'alpha_vantage', TRUE, CURRENT_TIMESTAMP),
('usd_cny', '美元人民币', 'USDCNY', '美元兑人民币汇率', 'currency', 'alpha_vantage', TRUE, CURRENT_TIMESTAMP);
```

#### MarketData 表

```sql
CREATE TABLE market_data (
    id TEXT PRIMARY KEY,
    indicator_id TEXT NOT NULL,
    value DECIMAL(15,4) NOT NULL,
    change_amount DECIMAL(15,4),
    change_percentage DECIMAL(5,2),
    record_date DATE NOT NULL,
    source TEXT,
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (indicator_id) REFERENCES market_indicators(id)
);

CREATE INDEX idx_market_data_indicator_id ON market_data(indicator_id);
CREATE INDEX idx_market_data_record_date ON market_data(record_date);
CREATE UNIQUE INDEX idx_market_data_unique ON market_data(indicator_id, record_date);
```

### 6. 日历事件模块

#### CalendarEvents 表

```sql
CREATE TABLE calendar_events (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    title TEXT NOT NULL,
    description TEXT,
    event_type TEXT, -- 'payment', 'investment', 'review', 'custom'
    start_time DATETIME NOT NULL,
    end_time DATETIME,
    is_recurring BOOLEAN DEFAULT FALSE,
    recurrence_rule TEXT, -- JSON格式的重复规则
    metadata TEXT, -- JSON格式
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_calendar_events_user_id ON calendar_events(user_id);
CREATE INDEX idx_calendar_events_start_time ON calendar_events(start_time);
CREATE INDEX idx_calendar_events_event_type ON calendar_events(event_type);
```

## 数据库优化策略

### 1. 索引策略

- 主键自动索引
- 外键字段索引
- 查询频繁的字段索引
- 复合索引用于复杂查询

### 2. 数据分区

- 按时间分区历史数据表
- 按用户分区大表数据

### 3. 数据清理

- 定期清理过期的 AI 对话记录
- 压缩历史市场数据
- 归档旧的新闻数据

### 4. 备份策略

- 每日自动备份
- 增量备份机制
- 多地备份存储

## Drizzle ORM 配置示例

```typescript
// schema.ts
import { sqliteTable, text, integer, real, blob } from 'drizzle-orm/sqlite-core'
import { relations } from 'drizzle-orm'

export const users = sqliteTable('users', {
  id: text('id').primaryKey(),
  email: text('email').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  name: text('name').notNull(),
  avatarUrl: text('avatar_url'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
})

export const assets = sqliteTable('assets', {
  id: text('id').primaryKey(),
  userId: text('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  categoryId: text('category_id').notNull(),
  name: text('name').notNull(),
  description: text('description'),
  currentValue: real('current_value').notNull(),
  purchasePrice: real('purchase_price'),
  purchaseDate: text('purchase_date'),
  currency: text('currency').default('USD'),
  metadata: text('metadata'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  assets: many(assets),
  debts: many(debts),
  conversations: many(aiConversations),
}))

export const assetsRelations = relations(assets, ({ one, many }) => ({
  user: one(users, {
    fields: [assets.userId],
    references: [users.id],
  }),
  history: many(assetHistory),
}))
```
