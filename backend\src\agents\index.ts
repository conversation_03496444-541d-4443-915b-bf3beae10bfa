/**
 * Agents Module - 财务分析AI代理系统
 *
 * 这个模块包含了完整的财务分析AI代理系统，基于LangGraph StateGraph实现
 * 包括财务分析、投资建议、风险评估、新闻分析和协调代理
 */

// 基础代理类
export { BaseAgent } from './base/BaseAgent'

// 专业代理
export { FinancialAnalysisAgent } from './financialAnalysisAgent'
export { InvestmentAdvisorAgent } from './investmentAdvisorAgent'
export { RiskAssessmentAgent } from './riskAssessmentAgent'
export { NewsAnalysisAgent } from './newsAnalysisAgent'
export { CoordinatorAgent } from './coordinatorAgent'

// 工作流图
export {
  FinancialWorkflowStateGraph,
  createFinancialStateGraphWorkflow,
  createFinancialWorkflow,
  FinancialWorkflowGraph,
  STATEGRAPH_WORKFLOW_CONFIG,
  WORKFLOW_CONFIG,
} from './workflowGraph'

// 重新导入类型以避免循环依赖
import { FinancialWorkflowStateGraph } from './workflowGraph'
import { FinancialAnalysisAgent } from './financialAnalysisAgent'
import { InvestmentAdvisorAgent } from './investmentAdvisorAgent'
import { RiskAssessmentAgent } from './riskAssessmentAgent'
import { NewsAnalysisAgent } from './newsAnalysisAgent'
import { CoordinatorAgent } from './coordinatorAgent'

// 工具函数
export { tools } from './tools'
export {
  getUserAssets,
  getUserDebts,
  getUserProfile,
  calculateTotalAssets,
  calculateTotalDebts,
  calculateNetWorth,
  analyzeAssetAllocation,
  calculateFinancialHealthScore,
  getMarketData,
  getFinancialNews,
  calculatePortfolioRisk,
  generateInvestmentRecommendations,
} from './tools'

// 类型定义
export type {
  WorkflowState,
  StateGraphNode,
  StateGraphCondition,
  StateGraphConfig,
  StateGraphEdge,
  StateGraphConditionalEdge,
  StateGraphExecutionContext,
  StateGraphError,
  StateGraphCheckpoint,
  StateGraphWorkflowInstance,
  NodeExecutionState,
  FinancialAnalysisResult,
  InvestmentAdviceResult,
  RiskAssessmentResult,
  NewsAnalysisResult,
  AssetAllocation,
  CashFlowAnalysis,
  InvestmentRecommendation,
  RiskFactor,
  VolatilityAnalysis,
  NewsItem,
  NewsImpact,
  WorkflowMemory,
  ConversationMemory,
  UserPreferences,
  PreviousAnalysis,
  Learning,
  AgentConfig,
  WorkflowConfig,
  WorkflowNode,
  WorkflowExecutionResult,
  ExecutionStep,
  ToolCallResult,
} from '../types/agents'

/**
 * 创建完整的财务分析工作流实例
 *
 * @param modelId - AI模型ID，默认为 'deepseek-chat'
 * @returns 配置好的财务分析工作流实例
 *
 * @example
 * ```typescript
 * import { createFinancialWorkflowInstance } from './agents'
 *
 * const workflow = createFinancialWorkflowInstance('deepseek-chat')
 *
 * const result = await workflow.execute({
 *   userQuery: '请分析我的财务状况',
 *   userId: 'user123',
 *   conversationId: 'conv456',
 *   modelId: 'deepseek-chat',
 *   messages: [],
 *   currentStep: 'start',
 *   nextSteps: [],
 *   isComplete: false,
 *   context: {},
 *   memory: {
 *     conversationHistory: [],
 *     userPreferences: {
 *       riskTolerance: 'moderate',
 *       investmentGoals: [],
 *       timeHorizon: '1-3年',
 *       preferredAssetTypes: [],
 *       excludedAssetTypes: [],
 *     },
 *     previousAnalyses: [],
 *     learnings: [],
 *   },
 * })
 * ```
 */
export const createFinancialWorkflowInstance = (
  modelId: string = 'deepseek-chat'
): FinancialWorkflowStateGraph => {
  return new FinancialWorkflowStateGraph(modelId)
}

/**
 * 创建单个代理实例
 *
 * @param agentType - 代理类型
 * @param modelId - AI模型ID
 * @returns 代理实例
 */
export const createAgent = (
  agentType: 'financial' | 'investment' | 'risk' | 'news' | 'coordinator',
  modelId: string = 'deepseek-chat'
) => {
  switch (agentType) {
    case 'financial':
      return new FinancialAnalysisAgent(modelId)
    case 'investment':
      return new InvestmentAdvisorAgent(modelId)
    case 'risk':
      return new RiskAssessmentAgent(modelId)
    case 'news':
      return new NewsAnalysisAgent(modelId)
    case 'coordinator':
      return new CoordinatorAgent(modelId)
    default:
      throw new Error(`未知的代理类型: ${agentType}`)
  }
}

/**
 * 代理系统配置
 */
export const AGENTS_CONFIG = {
  // 支持的AI模型
  supportedModels: [
    'deepseek-chat',
    'gpt-3.5-turbo',
    'gpt-4',
    'gpt-4-turbo',
    'claude-3-sonnet',
    'claude-3-opus',
  ],

  // 默认配置
  defaults: {
    modelId: 'deepseek-chat',
    temperature: 0.3,
    maxIterations: 3,
    maxExecutionTime: 300000, // 5分钟
    maxRetries: 3,
  },

  // 代理描述
  agentDescriptions: {
    financial: '专业的财务分析代理，负责分析用户的资产负债情况，提供财务健康度评估',
    investment: '专业的投资顾问代理，基于市场数据和用户风险偏好提供投资建议',
    risk: '专业的风险评估代理，评估投资风险和资产配置合理性',
    news: '专业的新闻分析代理，分析金融新闻对用户投资组合的影响',
    coordinator: '协调代理，整合各专业代理的分析结果，提供综合性的财务建议',
  },

  // 工作流配置
  workflow: {
    name: 'FinancialAnalysisStateGraphWorkflow',
    description: '基于LangGraph StateGraph的综合财务分析工作流',
    version: '2.0.0',
    enableParallelExecution: false,
    enableConditionalRouting: true,
    enableErrorRecovery: true,
    enableStateCheckpoints: true,
  },
} as const

/**
 * 验证代理系统健康状态
 *
 * @param workflow - 工作流实例
 * @returns 健康检查结果
 */
export const validateAgentsHealth = async (
  workflow: FinancialWorkflowStateGraph
): Promise<{
  healthy: boolean
  issues: string[]
  agentStatus: Record<string, boolean>
}> => {
  const issues: string[] = []
  const agentStatus: Record<string, boolean> = {}

  try {
    // 检查工作流健康状态
    const workflowHealthy = await workflow.healthCheck()
    if (!workflowHealthy) {
      issues.push('工作流健康检查失败')
    }

    // 检查各个代理
    const agentInfo = workflow.getAgentInfo()
    for (const agent of agentInfo) {
      agentStatus[agent.name] = true // 简化检查，实际可以更详细
    }

    return {
      healthy: issues.length === 0,
      issues,
      agentStatus,
    }
  } catch (error) {
    issues.push(`健康检查异常: ${error instanceof Error ? error.message : '未知错误'}`)
    return {
      healthy: false,
      issues,
      agentStatus,
    }
  }
}

/**
 * 获取代理系统信息
 */
export const getAgentsInfo = () => ({
  version: '2.0.0',
  description: '基于LangGraph StateGraph的财务分析AI代理系统',
  agents: Object.entries(AGENTS_CONFIG.agentDescriptions).map(
    ([type, description]) => ({
      type,
      description,
    })
  ),
  supportedModels: AGENTS_CONFIG.supportedModels,
  workflow: AGENTS_CONFIG.workflow,
})
