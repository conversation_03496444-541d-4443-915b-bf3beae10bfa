import {
  BaseMessage,
  HumanMessage,
  AIMessage,
  SystemMessage,
} from '@langchain/core/messages'
import { BaseAgent } from './base/BaseAgent'
import { logger } from '../utils/logger'
import type { WorkflowState, AgentConfig } from '../types/agents'

export class CoordinatorAgent extends BaseAgent {
  constructor(modelId: string = 'deepseek-chat') {
    const config: AgentConfig = {
      name: 'CoordinatorAgent',
      description: '协调代理，整合各专业代理的分析结果，提供综合性的财务建议',
      systemPrompt: `你是一位资深的财务规划师和投资顾问，负责协调和整合各个专业领域的分析结果。你的任务是：

1. 综合财务分析、投资建议、风险评估和新闻分析的结果
2. 识别各分析结果之间的关联性和一致性
3. 解决不同建议之间的冲突和矛盾
4. 提供优先级排序的综合建议
5. 制定具体的行动计划和时间表

协调原则：
- 以用户的财务目标和风险承受能力为核心
- 平衡短期需求和长期规划
- 考虑市场环境和时机因素
- 确保建议的可操作性和实用性
- 提供清晰的执行步骤和监控指标

请基于所有可用信息，提供全面、平衡、可执行的财务规划建议。`,
      tools: [],
      maxIterations: 2,
      temperature: 0.3,
      modelId,
    }

    super(config)
  }

  protected initializeTools(): void {
    // 协调代理不需要额外的工具，主要依赖其他代理的结果
  }

  protected async processTask(
    state: WorkflowState,
    messages: BaseMessage[]
  ): Promise<any> {
    try {
      logger.info('开始协调分析')

      // 构建综合分析提示
      const coordinationPrompt = this.buildCoordinationPrompt(state)

      // 生成综合建议
      const coordinationMessages = [...messages, new HumanMessage(coordinationPrompt)]

      const aiResponse = await this.generateResponse(coordinationMessages)

      // 解析综合建议
      const comprehensiveAdvice = this.parseComprehensiveAdvice(aiResponse, state)

      logger.info('协调分析完成')

      return comprehensiveAdvice
    } catch (error) {
      logger.error('协调分析失败:', error)
      throw error
    }
  }

  protected async updateState(
    state: WorkflowState,
    result: any
  ): Promise<WorkflowState> {
    return {
      ...state,
      currentStep: 'coordination_complete',
      nextSteps: [],
      isComplete: true,
      context: {
        ...state.context,
        coordinationComplete: true,
        comprehensiveAdvice: result,
      },
    }
  }

  private buildCoordinationPrompt(state: WorkflowState): string {
    const sections: string[] = []

    // 用户基本信息
    sections.push(`
## 用户查询
${state.userQuery}

## 用户基本信息
- 用户ID: ${state.userId}
- 对话ID: ${state.conversationId}
    `)

    // 财务分析结果
    if (state.financialAnalysis) {
      const fa = state.financialAnalysis
      sections.push(`
## 财务分析结果
- 总资产: ¥${fa.totalAssets?.toLocaleString() || '0'}
- 总负债: ¥${fa.totalDebts?.toLocaleString() || '0'}
- 净资产: ¥${fa.netWorth?.toLocaleString() || '0'}
- 财务健康评分: ${fa.financialHealthScore || 0}/100
- 月收入: ¥${fa.cashFlow?.monthlyIncome?.toLocaleString() || '0'}
- 月支出: ¥${fa.cashFlow?.monthlyExpenses?.toLocaleString() || '0'}
- 净现金流: ¥${fa.cashFlow?.netCashFlow?.toLocaleString() || '0'}

### 资产配置
${
  fa.assetAllocation
    ?.map(
      (item) =>
        `- ${item.category}: ¥${item.value?.toLocaleString() || '0'} (${
          item.percentage?.toFixed(1) || '0'
        }%, 风险: ${item.risk})`
    )
    .join('\n') || '暂无数据'
}

### 财务建议
${fa.recommendations?.map((rec) => `- ${rec}`).join('\n') || '暂无建议'}
      `)
    }

    // 投资建议结果
    if (state.investmentAdvice) {
      const ia = state.investmentAdvice
      sections.push(`
## 投资建议结果
- 风险偏好: ${ia.riskProfile}
- 预期收益率: ${(ia.expectedReturn * 100).toFixed(1)}%
- 投资时间周期: ${ia.timeHorizon}

### 推荐资产配置
${
  ia.recommendedAllocation
    ?.map((item) => `- ${item.category}: ${item.percentage}% (风险: ${item.risk})`)
    .join('\n') || '暂无数据'
}

### 具体投资建议
${
  ia.specificRecommendations
    ?.map(
      (rec) => `- ${rec.action} ${rec.asset}: ${rec.reason} (优先级: ${rec.priority})`
    )
    .join('\n') || '暂无建议'
}

### 投资逻辑
${ia.reasoning || '暂无详细说明'}
      `)
    }

    // 风险评估结果
    if (state.riskAssessment) {
      const ra = state.riskAssessment
      sections.push(`
## 风险评估结果
- 整体风险等级: ${ra.overallRisk}
- 多样化评分: ${ra.diversificationScore}/100
- 组合波动率: ${(ra.volatilityAnalysis?.portfolioVolatility * 100).toFixed(2)}%
- 夏普比率: ${ra.volatilityAnalysis?.sharpeRatio?.toFixed(2) || '0'}
- 最大回撤: ${(ra.volatilityAnalysis?.maxDrawdown * 100).toFixed(2)}%
- 风险价值(VaR): ¥${ra.volatilityAnalysis?.valueAtRisk?.toLocaleString() || '0'}

### 识别的风险因素
${
  ra.riskFactors
    ?.map((risk) => `- ${risk.type} (${risk.level}): ${risk.description}`)
    .join('\n') || '暂无风险因素'
}

### 风险控制建议
${ra.recommendations?.map((rec) => `- ${rec}`).join('\n') || '暂无建议'}
      `)
    }

    // 新闻分析结果
    if (state.newsAnalysis) {
      const na = state.newsAnalysis
      sections.push(`
## 新闻分析结果
- 市场情绪: ${na.marketSentiment}
- 相关新闻数量: ${na.relevantNews?.length || 0}

### 新闻影响分析
${
  na.impactAnalysis
    ?.map(
      (impact) =>
        `- ${impact.asset}: ${impact.impact} (置信度: ${(
          impact.confidence * 100
        ).toFixed(0)}%) - ${impact.reasoning}`
    )
    .join('\n') || '暂无影响分析'
}

### 行动洞察
${na.actionableInsights?.map((insight) => `- ${insight}`).join('\n') || '暂无洞察'}
      `)
    }

    const prompt = `
作为资深财务规划师，请基于以下所有分析结果，为用户提供综合性的财务建议：

${sections.join('\n')}

请提供：

## 1. 综合评估总结
- 用户当前财务状况的整体评价
- 各项分析结果的一致性和关联性分析
- 主要优势和需要改进的方面

## 2. 优先级建议
请按照紧急程度和重要性，提供具体的行动建议：

### 立即执行 (1-2周内)
- 需要立即处理的紧急事项
- 风险控制措施
- 现金流优化

### 短期规划 (1-3个月)
- 投资组合调整
- 资产配置优化
- 债务管理

### 中期规划 (3-12个月)
- 投资策略实施
- 财务目标设定
- 定期监控计划

### 长期规划 (1年以上)
- 长期投资布局
- 财富积累策略
- 退休规划考虑

## 3. 具体执行方案
- 详细的操作步骤
- 预期效果和时间表
- 监控指标和调整机制

## 4. 风险提示
- 需要特别注意的风险点
- 市场变化的应对策略
- 定期评估的重要性

请确保建议具体可行，优先级清晰，并考虑用户的实际情况和市场环境。
    `.trim()

    return prompt
  }

  private parseComprehensiveAdvice(aiResponse: string, state: WorkflowState): any {
    const advice = {
      summary: '',
      prioritizedRecommendations: {
        immediate: [] as string[],
        shortTerm: [] as string[],
        mediumTerm: [] as string[],
        longTerm: [] as string[],
      },
      executionPlan: [] as string[],
      riskWarnings: [] as string[],
      monitoringPoints: [] as string[],
      fullAnalysis: aiResponse,
      timestamp: new Date(),
    }

    const lines = aiResponse.split('\n')
    let currentSection = ''

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 识别章节
      if (trimmedLine.includes('综合评估') || trimmedLine.includes('总结')) {
        currentSection = 'summary'
        continue
      } else if (trimmedLine.includes('立即执行') || trimmedLine.includes('1-2周')) {
        currentSection = 'immediate'
        continue
      } else if (trimmedLine.includes('短期规划') || trimmedLine.includes('1-3个月')) {
        currentSection = 'shortTerm'
        continue
      } else if (trimmedLine.includes('中期规划') || trimmedLine.includes('3-12个月')) {
        currentSection = 'mediumTerm'
        continue
      } else if (trimmedLine.includes('长期规划') || trimmedLine.includes('1年以上')) {
        currentSection = 'longTerm'
        continue
      } else if (trimmedLine.includes('执行方案') || trimmedLine.includes('操作步骤')) {
        currentSection = 'execution'
        continue
      } else if (trimmedLine.includes('风险提示') || trimmedLine.includes('注意')) {
        currentSection = 'risks'
        continue
      } else if (trimmedLine.includes('监控') || trimmedLine.includes('评估')) {
        currentSection = 'monitoring'
        continue
      }

      // 提取内容
      if (trimmedLine.startsWith('-') || trimmedLine.match(/^\d+\./)) {
        const content = trimmedLine.replace(/^[-\d.]\s*/, '').trim()
        if (content.length > 5) {
          switch (currentSection) {
            case 'immediate':
              advice.prioritizedRecommendations.immediate.push(content)
              break
            case 'shortTerm':
              advice.prioritizedRecommendations.shortTerm.push(content)
              break
            case 'mediumTerm':
              advice.prioritizedRecommendations.mediumTerm.push(content)
              break
            case 'longTerm':
              advice.prioritizedRecommendations.longTerm.push(content)
              break
            case 'execution':
              advice.executionPlan.push(content)
              break
            case 'risks':
              advice.riskWarnings.push(content)
              break
            case 'monitoring':
              advice.monitoringPoints.push(content)
              break
          }
        }
      } else if (currentSection === 'summary' && trimmedLine.length > 20) {
        advice.summary += trimmedLine + ' '
      }
    }

    // 清理总结文本
    advice.summary = advice.summary.trim()

    // 如果某些部分为空，添加默认内容
    if (advice.prioritizedRecommendations.immediate.length === 0) {
      advice.prioritizedRecommendations.immediate.push(
        '审查当前财务状况，确保应急资金充足'
      )
    }

    if (advice.executionPlan.length === 0) {
      advice.executionPlan.push(
        '制定详细的财务计划和时间表',
        '开始实施优先级最高的建议',
        '建立定期监控和评估机制'
      )
    }

    if (advice.riskWarnings.length === 0) {
      advice.riskWarnings.push(
        '市场存在不确定性，投资需谨慎',
        '定期评估和调整投资策略',
        '保持适当的风险分散'
      )
    }

    return advice
  }
}
