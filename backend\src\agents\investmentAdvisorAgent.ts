import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages'
import { BaseAgent } from './base/BaseAgent'
import { logger } from '../utils/logger'
import type {
  WorkflowState,
  AgentConfig,
  InvestmentAdviceResult,
  InvestmentRecommendation,
  AssetAllocation,
} from '../types/agents'
import {
  getUserAssets,
  getUserProfile,
  analyzeAssetAllocation,
  generateInvestmentRecommendations,
  getMarketData,
} from './tools'

export class InvestmentAdvisorAgent extends BaseAgent {
  constructor(modelId: string = 'deepseek-chat') {
    const config: AgentConfig = {
      name: 'InvestmentAdvisorAgent',
      description: '专业的投资顾问代理，基于市场数据和用户风险偏好提供投资建议',
      systemPrompt: `你是一位经验丰富的投资顾问，具有深厚的金融市场知识和投资策略经验。你的任务是：

1. 分析用户的风险承受能力和投资目标
2. 基于市场数据和趋势提供投资建议
3. 制定合理的资产配置策略
4. 推荐具体的投资产品和操作
5. 考虑投资时间周期和流动性需求

投资建议原则：
- 风险与收益匹配
- 分散投资降低风险
- 长期投资价值导向
- 考虑市场周期和时机
- 关注成本和税务效率
- 定期调整和再平衡

请基于用户的具体情况，提供个性化、专业的投资建议。避免过于激进或保守的建议，注重实用性和可操作性。`,
      tools: [
        'getUserAssets',
        'getUserProfile',
        'analyzeAssetAllocation',
        'generateInvestmentRecommendations',
        'getMarketData',
      ],
      maxIterations: 3,
      temperature: 0.4,
      modelId,
    }

    super(config)
  }

  protected initializeTools(): void {
    this.tools.set('getUserAssets', getUserAssets)
    this.tools.set('getUserProfile', getUserProfile)
    this.tools.set('analyzeAssetAllocation', analyzeAssetAllocation)
    this.tools.set(
      'generateInvestmentRecommendations',
      generateInvestmentRecommendations
    )
    this.tools.set('getMarketData', getMarketData)
  }

  protected async processTask(
    state: WorkflowState,
    messages: BaseMessage[]
  ): Promise<InvestmentAdviceResult> {
    try {
      logger.info('开始投资建议分析')

      // 1. 获取用户数据
      const assetsResult = await this.callTool('getUserAssets', state.userId)
      const profileResult = await this.callTool('getUserProfile', state.userId)

      if (!assetsResult.success) {
        throw new Error('获取用户资产数据失败')
      }

      const assets = assetsResult.output?.data || []
      const profile = profileResult.output?.data

      // 2. 确定用户风险偏好
      const riskProfile = this.determineRiskProfile(profile, state.financialAnalysis)

      // 3. 分析当前资产配置
      const allocationResult = await this.callTool('analyzeAssetAllocation', assets)
      const currentAllocation = allocationResult.success
        ? allocationResult.output?.data || []
        : []

      // 4. 获取市场数据
      const marketSymbols = this.extractMarketSymbols(assets)
      const marketDataResult = await this.callTool('getMarketData', marketSymbols)
      const marketData = marketDataResult.success
        ? marketDataResult.output?.data || []
        : []

      // 5. 生成投资建议
      const recommendationsResult = await this.callTool(
        'generateInvestmentRecommendations',
        {
          assets,
          riskProfile,
        }
      )
      const baseRecommendations: InvestmentRecommendation[] =
        recommendationsResult.success ? recommendationsResult.output?.data || [] : []

      // 6. 计算推荐资产配置
      const recommendedAllocation = this.calculateRecommendedAllocation(
        riskProfile,
        currentAllocation
      )

      // 7. 使用AI生成详细投资建议
      const advicePrompt = this.buildAdvicePrompt({
        assets,
        currentAllocation,
        recommendedAllocation,
        riskProfile,
        marketData,
        financialAnalysis: state.financialAnalysis,
        baseRecommendations,
      })

      const adviceMessages = [...messages, new HumanMessage(advicePrompt)]
      const aiResponse = await this.generateResponse(adviceMessages)

      // 8. 解析AI建议
      const { specificRecommendations, reasoning, expectedReturn, timeHorizon } =
        this.parseAIAdvice(aiResponse, baseRecommendations)

      const result: InvestmentAdviceResult = {
        riskProfile,
        recommendedAllocation,
        specificRecommendations,
        timeHorizon,
        expectedReturn,
        reasoning,
        timestamp: new Date(),
      }

      logger.info('投资建议分析完成', {
        riskProfile,
        recommendationsCount: specificRecommendations.length,
        expectedReturn,
      })

      return result
    } catch (error) {
      logger.error('投资建议分析失败:', error)
      throw error
    }
  }

  protected async updateState(
    state: WorkflowState,
    result: InvestmentAdviceResult
  ): Promise<WorkflowState> {
    return {
      ...state,
      investmentAdvice: result,
      currentStep: 'investment_advice_complete',
      nextSteps: ['risk_assessment', 'news_analysis'],
      context: {
        ...state.context,
        investmentAdviceComplete: true,
        riskProfile: result.riskProfile,
        expectedReturn: result.expectedReturn,
      },
    }
  }

  private determineRiskProfile(
    profile: any,
    financialAnalysis?: any
  ): 'conservative' | 'moderate' | 'aggressive' {
    // 基于用户设置的风险偏好
    if (profile?.riskTolerance) {
      return profile.riskTolerance
    }

    // 基于财务分析结果推断
    if (financialAnalysis) {
      const { financialHealthScore, netWorth } = financialAnalysis

      if (financialHealthScore >= 80 && netWorth > 500000) {
        return 'aggressive'
      } else if (financialHealthScore >= 60 && netWorth > 100000) {
        return 'moderate'
      } else {
        return 'conservative'
      }
    }

    // 默认为稳健型
    return 'moderate'
  }

  private extractMarketSymbols(assets: any[]): string[] {
    const symbols: string[] = []

    assets.forEach((asset) => {
      if (asset.symbol) {
        symbols.push(asset.symbol)
      } else if (asset.categoryId === '股票' || asset.categoryId === 'stocks') {
        // 添加一些常见的市场指数
        symbols.push('SPY', 'QQQ', 'IWM')
      }
    })

    // 添加基准指数
    if (symbols.length === 0) {
      symbols.push('SPY', 'BND', 'GLD', 'VTI')
    }

    return [...new Set(symbols)].slice(0, 10) // 去重并限制数量
  }

  private calculateRecommendedAllocation(
    riskProfile: 'conservative' | 'moderate' | 'aggressive',
    currentAllocation: AssetAllocation[]
  ): AssetAllocation[] {
    const allocations = {
      conservative: [
        { category: '现金', percentage: 20, risk: 'low' as const },
        { category: '债券', percentage: 50, risk: 'low' as const },
        { category: '股票', percentage: 20, risk: 'medium' as const },
        { category: '房地产', percentage: 5, risk: 'medium' as const },
        { category: '其他', percentage: 5, risk: 'medium' as const },
      ],
      moderate: [
        { category: '现金', percentage: 10, risk: 'low' as const },
        { category: '债券', percentage: 30, risk: 'low' as const },
        { category: '股票', percentage: 50, risk: 'medium' as const },
        { category: '房地产', percentage: 5, risk: 'medium' as const },
        { category: '其他', percentage: 5, risk: 'high' as const },
      ],
      aggressive: [
        { category: '现金', percentage: 5, risk: 'low' as const },
        { category: '债券', percentage: 15, risk: 'low' as const },
        { category: '股票', percentage: 65, risk: 'high' as const },
        { category: '房地产', percentage: 10, risk: 'medium' as const },
        { category: '其他', percentage: 5, risk: 'high' as const },
      ],
    }

    return allocations[riskProfile].map((item) => ({
      ...item,
      value: 0, // 这里应该基于总资产计算具体金额
    }))
  }

  private buildAdvicePrompt(data: {
    assets: any[]
    currentAllocation: AssetAllocation[]
    recommendedAllocation: AssetAllocation[]
    riskProfile: string
    marketData: any[]
    financialAnalysis?: any
    baseRecommendations: InvestmentRecommendation[]
  }): string {
    return `
请基于以下信息提供专业的投资建议：

## 用户风险偏好
${data.riskProfile} (保守型/稳健型/积极型)

## 当前资产配置
${data.currentAllocation
  .map(
    (item) =>
      `- ${item.category}：${item.percentage.toFixed(
        1
      )}% (¥${item.value.toLocaleString()})`
  )
  .join('\n')}

## 建议资产配置
${data.recommendedAllocation
  .map((item) => `- ${item.category}：${item.percentage}% (风险：${item.risk})`)
  .join('\n')}

## 市场数据
${data.marketData
  .map(
    (item) =>
      `- ${item.symbol}：¥${item.price?.toFixed(2)} (${item.changePercent?.toFixed(
        2
      )}%)`
  )
  .join('\n')}

## 财务分析结果
${
  data.financialAnalysis
    ? `
- 净资产：¥${data.financialAnalysis.netWorth?.toLocaleString() || '0'}
- 财务健康评分：${data.financialAnalysis.financialHealthScore || 0}/100
- 月现金流：¥${data.financialAnalysis.cashFlow?.netCashFlow?.toLocaleString() || '0'}
`
    : '暂无财务分析数据'
}

## 基础建议
${data.baseRecommendations
  .map(
    (rec) =>
      `- ${rec.action} ${rec.asset} (${rec.percentage?.toFixed(1) || '0'}%): ${
        rec.reason
      }`
  )
  .join('\n')}

请提供：
1. 投资策略总体分析
2. 具体投资建议（包括买入/卖出/持有的具体操作）
3. 预期收益率估算
4. 投资时间周期建议
5. 风险控制措施
6. 市场时机分析

请确保建议具体可操作，并说明投资逻辑和预期效果。
    `.trim()
  }

  private parseAIAdvice(
    aiResponse: string,
    baseRecommendations: InvestmentRecommendation[]
  ): {
    specificRecommendations: InvestmentRecommendation[]
    reasoning: string
    expectedReturn: number
    timeHorizon: string
  } {
    const recommendations: InvestmentRecommendation[] = [...baseRecommendations]
    let reasoning = aiResponse
    let expectedReturn = 0
    let timeHorizon = '1-3年'

    // 提取预期收益率
    const returnMatch = aiResponse.match(/预期收益.*?(\d+(?:\.\d+)?)%/i)
    if (returnMatch && returnMatch[1]) {
      expectedReturn = parseFloat(returnMatch[1])
    } else {
      // 基于风险偏好设置默认预期收益
      expectedReturn = 6 // 默认6%
    }

    // 提取时间周期
    const timeMatch = aiResponse.match(/时间.*?(\d+[-~]\d+年|\d+年以上|\d+个月)/i)
    if (timeMatch && timeMatch[1]) {
      timeHorizon = timeMatch[1]
    }

    // 提取具体投资建议
    const lines = aiResponse.split('\n')
    for (const line of lines) {
      const trimmedLine = line.trim()

      // 识别投资操作建议
      if (trimmedLine.includes('买入') || trimmedLine.includes('增持')) {
        const assetMatch = trimmedLine.match(/买入|增持.*?([^，。]+)/)
        if (assetMatch && assetMatch[1]) {
          recommendations.push({
            action: 'buy',
            asset: assetMatch[1].trim(),
            reason: trimmedLine,
            priority: 'medium',
          })
        }
      } else if (trimmedLine.includes('卖出') || trimmedLine.includes('减持')) {
        const assetMatch = trimmedLine.match(/卖出|减持.*?([^，。]+)/)
        if (assetMatch && assetMatch[1]) {
          recommendations.push({
            action: 'sell',
            asset: assetMatch[1].trim(),
            reason: trimmedLine,
            priority: 'medium',
          })
        }
      }
    }

    return {
      specificRecommendations: recommendations.slice(0, 15), // 限制建议数量
      reasoning,
      expectedReturn,
      timeHorizon,
    }
  }
}
