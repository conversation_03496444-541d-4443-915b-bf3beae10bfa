import { create } from 'zustand'
import type { Asset, Debt, AIConversation, NewsArticle, MarketData } from '../types'

interface AppState {
  // 资产数据
  assets: Asset[]
  assetsLoading: boolean

  // 负债数据
  debts: Debt[]
  debtsLoading: boolean

  // AI对话数据
  conversations: AIConversation[]
  currentConversation: AIConversation | null
  aiLoading: boolean

  // 新闻数据
  news: NewsArticle[]
  newsLoading: boolean

  // 市场数据
  marketData: MarketData[]
  marketDataLoading: boolean

  // UI状态
  sidebarCollapsed: boolean
  theme: 'light' | 'dark' | 'system'
}

interface AppActions {
  // 资产操作
  setAssets: (assets: Asset[]) => void
  addAsset: (asset: Asset) => void
  updateAsset: (id: string, asset: Partial<Asset>) => void
  removeAsset: (id: string) => void
  setAssetsLoading: (loading: boolean) => void

  // 负债操作
  setDebts: (debts: Debt[]) => void
  addDebt: (debt: Debt) => void
  updateDebt: (id: string, debt: Partial<Debt>) => void
  removeDebt: (id: string) => void
  setDebtsLoading: (loading: boolean) => void

  // AI对话操作
  setConversations: (conversations: AIConversation[]) => void
  setCurrentConversation: (conversation: AIConversation | null) => void
  addConversation: (conversation: AIConversation) => void
  setAiLoading: (loading: boolean) => void

  // 新闻操作
  setNews: (news: NewsArticle[]) => void
  setNewsLoading: (loading: boolean) => void

  // 市场数据操作
  setMarketData: (data: MarketData[]) => void
  setMarketDataLoading: (loading: boolean) => void

  // UI操作
  toggleSidebar: () => void
  setSidebarCollapsed: (collapsed: boolean) => void
  setTheme: (theme: 'light' | 'dark' | 'system') => void
}

type AppStore = AppState & AppActions

export const useAppStore = create<AppStore>((set) => ({
  // Initial State
  assets: [],
  assetsLoading: false,
  debts: [],
  debtsLoading: false,
  conversations: [],
  currentConversation: null,
  aiLoading: false,
  news: [],
  newsLoading: false,
  marketData: [],
  marketDataLoading: false,
  sidebarCollapsed: false,
  theme: 'system',

  // Asset Actions
  setAssets: (assets) => set({ assets }),
  addAsset: (asset) => set((state) => ({ assets: [...state.assets, asset] })),
  updateAsset: (id, updatedAsset) =>
    set((state) => ({
      assets: state.assets.map((asset) =>
        asset.id === id ? { ...asset, ...updatedAsset } : asset
      ),
    })),
  removeAsset: (id) =>
    set((state) => ({
      assets: state.assets.filter((asset) => asset.id !== id),
    })),
  setAssetsLoading: (assetsLoading) => set({ assetsLoading }),

  // Debt Actions
  setDebts: (debts) => set({ debts }),
  addDebt: (debt) => set((state) => ({ debts: [...state.debts, debt] })),
  updateDebt: (id, updatedDebt) =>
    set((state) => ({
      debts: state.debts.map((debt) =>
        debt.id === id ? { ...debt, ...updatedDebt } : debt
      ),
    })),
  removeDebt: (id) =>
    set((state) => ({
      debts: state.debts.filter((debt) => debt.id !== id),
    })),
  setDebtsLoading: (debtsLoading) => set({ debtsLoading }),

  // AI Actions
  setConversations: (conversations) => set({ conversations }),
  setCurrentConversation: (currentConversation) => set({ currentConversation }),
  addConversation: (conversation) =>
    set((state) => ({
      conversations: [...state.conversations, conversation],
    })),
  setAiLoading: (aiLoading) => set({ aiLoading }),

  // News Actions
  setNews: (news) => set({ news }),
  setNewsLoading: (newsLoading) => set({ newsLoading }),

  // Market Data Actions
  setMarketData: (marketData) => set({ marketData }),
  setMarketDataLoading: (marketDataLoading) => set({ marketDataLoading }),

  // UI Actions
  toggleSidebar: () => set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed })),
  setSidebarCollapsed: (sidebarCollapsed) => set({ sidebarCollapsed }),
  setTheme: (theme) => set({ theme }),
}))
