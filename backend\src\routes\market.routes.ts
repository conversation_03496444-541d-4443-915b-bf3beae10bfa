import { Router } from 'express'
import { marketController } from '../controllers/marketController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 获取市场指标列表（无需认证）
router.get('/indicators', async<PERSON>and<PERSON>(marketController.getMarketIndicators))

// 以下路由需要认证
router.use(authMiddleware)

// 获取市场概览
router.get('/overview', asyncHandler(marketController.getMarketOverview))

// 获取市场分析
router.get('/analysis', asyncHandler(marketController.getMarketAnalysis))

// 获取指定指标的市场数据
router.get('/data/:indicatorId', asyncHandler(marketController.getMarketData))

export default router
