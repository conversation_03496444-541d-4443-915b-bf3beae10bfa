# AI 驱动资产管理应用 - 开发指南

本文档为开发者提供了项目的开发环境搭建、代码结构说明、开发规范和贡献指南。

## 🛠️ 开发环境搭建

### 前置要求

- **Node.js**: 18.0+ (推荐 22.0+)
- **pnpm**: 8.0+
- **Git**: 2.30+
- **VSCode**: 推荐使用的 IDE
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 推荐的 VSCode 扩展

```json
{
  "recommendations": [
    "ms-vscode.vscode-typescript-next",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-eslint",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense",
    "ms-vscode.vscode-json"
  ]
}
```

### 快速开始

```bash
# 1. 克隆项目
git clone <repository-url>
cd ai-asset-manager

# 2. 安装依赖
pnpm install

# 3. 设置环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件

# 4. 启动开发环境
./deploy.sh dev

# 或者手动启动
pnpm run dev
```

## 📁 项目结构详解

```
ai-asset-manager/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # 可复用组件
│   │   │   ├── layout/     # 布局组件
│   │   │   ├── ui/         # UI基础组件
│   │   │   └── charts/     # 图表组件
│   │   ├── pages/          # 页面组件
│   │   │   ├── dashboard/  # 仪表板
│   │   │   ├── assets/     # 资产管理
│   │   │   ├── liabilities/# 负债管理
│   │   │   ├── ai-insights/# AI洞察
│   │   │   ├── news/       # 新闻页面
│   │   │   └── settings/   # 设置页面
│   │   ├── services/       # API服务
│   │   ├── stores/         # 状态管理
│   │   ├── hooks/          # 自定义Hooks
│   │   ├── utils/          # 工具函数
│   │   └── types/          # TypeScript类型
│   ├── public/             # 静态资源
│   └── package.json
├── backend/                  # Node.js后端服务
│   ├── src/
│   │   ├── controllers/    # 控制器层
│   │   ├── services/       # 业务逻辑层
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── database/       # 数据库相关
│   │   │   ├── connection.ts # 数据库连接
│   │   │   └── schema.ts   # 数据库模式
│   │   ├── ai/            # AI服务集成
│   │   ├── utils/         # 工具函数
│   │   └── types/         # TypeScript类型
│   └── package.json
├── data/                    # SQLite数据库文件
├── logs/                    # 应用日志
├── docs/                    # 项目文档
├── docker-compose.yml       # Docker编排
├── Dockerfile              # Docker构建
├── deploy.sh               # 部署脚本
└── package.json            # 根项目配置
```

## 🏗️ 架构设计

### 前端架构

**技术栈**:

- **React 18**: 用户界面框架
- **TypeScript**: 类型安全
- **Zustand**: 轻量级状态管理
- **Ant Design**: UI 组件库
- **Tailwind CSS**: 样式框架
- **Recharts**: 数据可视化
- **Vite**: 构建工具

**状态管理**:

```typescript
// stores/appStore.ts
interface AppState {
  user: User | null
  assets: Asset[]
  conversations: AIConversation[]
  // 状态管理逻辑
}
```

**组件设计原则**:

- 单一职责原则
- 可复用性优先
- Props 接口明确
- 错误边界处理

### 后端架构

**技术栈**:

- **Node.js 22**: 运行时环境
- **Express.js**: Web 框架
- **TypeScript**: 类型安全
- **SQLite + Drizzle ORM**: 数据持久化
- **Vercel AI SDK**: AI 集成
- **JWT**: 身份认证

**分层架构**:

```
Controller Layer (控制器层)
    ↓
Service Layer (业务逻辑层)
    ↓
Database Layer (数据访问层)
```

## 🔧 开发工作流

### Git 工作流

```bash
# 1. 创建功能分支
git checkout -b feature/new-feature

# 2. 开发和提交
git add .
git commit -m "feat: add new feature"

# 3. 推送分支
git push origin feature/new-feature

# 4. 创建Pull Request
# 5. 代码审查
# 6. 合并到主分支
```

### 提交信息规范

使用[Conventional Commits](https://www.conventionalcommits.org/)规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

**类型说明**:

- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

**示例**:

```
feat(auth): add user registration functionality
fix(api): resolve asset deletion error
docs: update deployment guide
```

### 代码规范

**TypeScript 规范**:

```typescript
// 使用接口定义类型
interface User {
  id: string
  email: string
  name: string
}

// 使用枚举定义常量
enum AssetType {
  CASH = 'cash',
  STOCK = 'stock',
  BOND = 'bond',
}

// 函数类型注解
const createUser = async (userData: CreateUserRequest): Promise<User> => {
  // 实现逻辑
}
```

**React 组件规范**:

```typescript
// 组件Props接口
interface AssetCardProps {
  asset: Asset
  onEdit: (asset: Asset) => void
  onDelete: (id: string) => void
}

// 函数组件
const AssetCard: React.FC<AssetCardProps> = ({ asset, onEdit, onDelete }) => {
  return <div className="asset-card">{/* 组件内容 */}</div>
}

export default AssetCard
```

## 🧪 测试策略

### 测试类型

**单元测试**:

```typescript
// utils/formatCurrency.test.ts
import { formatCurrency } from './formatCurrency'

describe('formatCurrency', () => {
  it('should format USD currency correctly', () => {
    expect(formatCurrency(1234.56, 'USD')).toBe('$1,234.56')
  })
})
```

**集成测试**:

```typescript
// services/authService.test.ts
import { authService } from './authService'

describe('AuthService', () => {
  it('should register user successfully', async () => {
    const userData = {
      username: 'testuser',
      email: '<EMAIL>',
      password: 'password123',
    }

    const result = await authService.register(userData)
    expect(result.user.email).toBe(userData.email)
  })
})
```

**E2E 测试**:

```typescript
// e2e/auth.spec.ts
import { test, expect } from '@playwright/test'

test('user can register and login', async ({ page }) => {
  await page.goto('/register')
  await page.fill('[data-testid=email]', '<EMAIL>')
  await page.fill('[data-testid=password]', 'password123')
  await page.click('[data-testid=submit]')

  await expect(page).toHaveURL('/dashboard')
})
```

### 运行测试

```bash
# 运行所有测试
pnpm test

# 运行特定测试文件
pnpm test auth.test.ts

# 运行测试并生成覆盖率报告
pnpm test:coverage

# 运行E2E测试
pnpm test:e2e
```

## 🔍 调试指南

### 前端调试

**React DevTools**:

- 安装 React DevTools 浏览器扩展
- 检查组件状态和 Props
- 性能分析

**浏览器调试**:

```typescript
// 使用console.log调试
console.log('Debug info:', { user, assets })

// 使用debugger断点
const handleSubmit = () => {
  debugger // 浏览器会在此处暂停
  // 处理逻辑
}
```

### 后端调试

**VSCode 调试配置**:

```json
// .vscode/launch.json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug Backend",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/backend/src/index.ts",
      "outFiles": ["${workspaceFolder}/backend/dist/**/*.js"],
      "runtimeArgs": ["-r", "ts-node/register"],
      "env": {
        "NODE_ENV": "development"
      }
    }
  ]
}
```

**日志调试**:

```typescript
import { logger } from '../utils/logger'

// 不同级别的日志
logger.debug('Debug information')
logger.info('General information')
logger.warn('Warning message')
logger.error('Error occurred', error)
```

## 📊 性能优化

### 前端优化

**代码分割**:

```typescript
// 路由级别的代码分割
const Dashboard = lazy(() => import('../pages/dashboard/Dashboard'))
const Assets = lazy(() => import('../pages/assets/AssetsPage'))

// 组件级别的代码分割
const HeavyComponent = lazy(() => import('./HeavyComponent'))
```

**状态优化**:

```typescript
// 使用useMemo缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data)
}, [data])

// 使用useCallback缓存函数
const handleClick = useCallback(() => {
  // 处理逻辑
}, [dependency])
```

### 后端优化

**数据库优化**:

```typescript
// 使用索引
await db.select().from(assets).where(eq(assets.userId, userId))

// 批量操作
await db.insert(assets).values(assetArray)

// 分页查询
await db
  .select()
  .from(assets)
  .limit(20)
  .offset(page * 20)
```

**缓存策略**:

```typescript
// 内存缓存
const cache = new Map()

const getCachedData = (key: string) => {
  if (cache.has(key)) {
    return cache.get(key)
  }

  const data = fetchData(key)
  cache.set(key, data)
  return data
}
```

## 🔐 安全开发

### 输入验证

```typescript
// 使用Zod进行数据验证
import { z } from 'zod'

const CreateUserSchema = z.object({
  username: z.string().min(3).max(20),
  email: z.string().email(),
  password: z.string().min(6),
})

// 在控制器中使用
const userData = CreateUserSchema.parse(req.body)
```

### 认证和授权

```typescript
// JWT中间件
const authMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const token = req.headers.authorization?.replace('Bearer ', '')

  if (!token) {
    return res.status(401).json({ error: 'No token provided' })
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET)
    req.user = decoded
    next()
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' })
  }
}
```

## 🚀 部署和发布

### 构建流程

```bash
# 构建前端
cd frontend
pnpm run build

# 构建后端
cd backend
pnpm run build

# 运行生产版本
pnpm run start
```

### CI/CD 配置

```yaml
# .github/workflows/deploy.yml
name: Deploy

on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '22'
      - run: pnpm install
      - run: pnpm run build
      - run: pnpm run test
      - name: Deploy
        run: ./deploy.sh prod
```

## 📚 学习资源

### 官方文档

- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Node.js Documentation](https://nodejs.org/docs/)
- [Vercel AI SDK](https://sdk.vercel.ai/docs)

### 推荐阅读

- [Clean Code](https://www.amazon.com/Clean-Code-Handbook-Software-Craftsmanship/dp/0132350882)
- [Effective TypeScript](https://effectivetypescript.com/)
- [React Patterns](https://reactpatterns.com/)

## 🤝 贡献指南

### 如何贡献

1. **Fork 项目**到你的 GitHub 账户
2. **创建功能分支**: `git checkout -b feature/amazing-feature`
3. **提交更改**: `git commit -m 'Add amazing feature'`
4. **推送分支**: `git push origin feature/amazing-feature`
5. **创建 Pull Request**

### Pull Request 规范

- 提供清晰的 PR 标题和描述
- 关联相关的 Issue
- 确保所有测试通过
- 更新相关文档
- 请求代码审查

### 代码审查清单

- [ ] 代码符合项目规范
- [ ] 包含适当的测试
- [ ] 文档已更新
- [ ] 没有安全漏洞
- [ ] 性能影响可接受
- [ ] 向后兼容性

## 🐛 问题报告

### Bug 报告模板

```markdown
**Bug 描述**
简要描述 bug 的现象

**复现步骤**

1. 进入 '...'
2. 点击 '....'
3. 滚动到 '....'
4. 看到错误

**期望行为**
描述你期望发生的情况

**截图**
如果适用，添加截图来帮助解释问题

**环境信息**

- OS: [e.g. iOS]
- Browser [e.g. chrome, safari]
- Version [e.g. 22]

**附加信息**
添加任何其他相关信息
```

---

**Happy Coding! 🎉**

如果你有任何问题或建议，欢迎创建 Issue 或联系维护团队。
