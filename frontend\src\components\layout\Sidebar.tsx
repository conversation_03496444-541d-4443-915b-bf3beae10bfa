import React from 'react'
import { Layout, Menu } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  DashboardOutlined,
  WalletOutlined,
  CreditCardOutlined,
  RobotOutlined,
  FileTextOutlined,
  SettingOutlined,
  BarChartOutlined,
} from '@ant-design/icons'
import { useAppStore } from '../../stores/appStore'

const { Sider } = Layout

export const Sidebar: React.FC = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { sidebarCollapsed } = useAppStore()

  const menuItems = [
    {
      key: '/',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/assets',
      icon: <WalletOutlined />,
      label: '资产管理',
    },
    {
      key: '/liabilities',
      icon: <CreditCardOutlined />,
      label: '负债管理',
    },
    {
      key: '/ai-insights',
      icon: <RobotOutlined />,
      label: 'AI 建议',
    },
    {
      key: '/news',
      icon: <FileTextOutlined />,
      label: '新闻中心',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={sidebarCollapsed}
      className="fixed left-0 top-0 bottom-0 z-10 bg-white shadow-lg"
      width={256}
      collapsedWidth={80}
    >
      <div className="h-16 flex items-center justify-center border-b border-gray-200">
        {!sidebarCollapsed ? (
          <div className="text-xl font-bold text-blue-600">AI 资产管理</div>
        ) : (
          <div className="text-2xl font-bold text-blue-600">AI</div>
        )}
      </div>

      <Menu
        mode="inline"
        selectedKeys={[location.pathname]}
        items={menuItems}
        onClick={handleMenuClick}
        className="border-r-0 h-full"
        style={{
          paddingTop: '16px',
        }}
      />
    </Sider>
  )
}
