import { Request, Response } from 'express'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'

export class MarketController {
  /**
   * 获取市场指标列表
   */
  async getMarketIndicators(req: Request, res: Response): Promise<void> {
    try {
      // 模拟市场指标数据
      const mockIndicators = [
        {
          id: 'sp500',
          name: 'S&P 500',
          symbol: 'SPX',
          description: '标普500指数',
          category: 'index',
          isActive: true,
          createdAt: new Date(),
        },
        {
          id: 'nasdaq',
          name: 'NASDAQ',
          symbol: 'IXIC',
          description: '纳斯达克综合指数',
          category: 'index',
          isActive: true,
          createdAt: new Date(),
        },
        {
          id: 'gold',
          name: '黄金',
          symbol: 'GOLD',
          description: '黄金现货价格',
          category: 'commodity',
          isActive: true,
          createdAt: new Date(),
        },
        {
          id: 'usd_cny',
          name: '美元人民币',
          symbol: 'USDCNY',
          description: '美元兑人民币汇率',
          category: 'currency',
          isActive: true,
          createdAt: new Date(),
        },
      ]

      res.json({
        success: true,
        data: mockIndicators,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取市场指标失败:', error)
      res.status(500).json({
        success: false,
        message: '获取市场指标失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取市场数据
   */
  async getMarketData(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { indicatorId } = req.params
      const { days = 30 } = req.query

      // 模拟市场数据
      const mockData = []
      const daysNum = parseInt(days as string, 10)
      const baseValue =
        indicatorId === 'sp500'
          ? 4400
          : indicatorId === 'nasdaq'
          ? 14200
          : indicatorId === 'gold'
          ? 2000
          : indicatorId === 'usd_cny'
          ? 7.2
          : 100

      for (let i = daysNum - 1; i >= 0; i--) {
        const date = new Date()
        date.setDate(date.getDate() - i)

        const randomChange = (Math.random() - 0.5) * 0.04 // ±2% 随机变化
        const value = baseValue * (1 + randomChange)
        const changeAmount = i === 0 ? value - baseValue : (Math.random() - 0.5) * 50
        const changePercentage = (changeAmount / baseValue) * 100

        mockData.push({
          id: `${indicatorId}-${i}`,
          indicatorId,
          value: parseFloat(value.toFixed(2)),
          changeAmount: parseFloat(changeAmount.toFixed(2)),
          changePercentage: parseFloat(changePercentage.toFixed(2)),
          recordDate: date.toISOString().split('T')[0],
          createdAt: new Date(),
        })
      }

      res.json({
        success: true,
        data: mockData,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取市场数据失败:', error)
      res.status(500).json({
        success: false,
        message: '获取市场数据失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取市场概览
   */
  async getMarketOverview(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // 模拟市场概览数据
      const mockOverview = {
        indices: [
          {
            name: 'S&P 500',
            symbol: 'SPX',
            value: 4400.25,
            change: 12.5,
            changePercent: 0.28,
            trend: 'up',
          },
          {
            name: 'NASDAQ',
            symbol: 'IXIC',
            value: 14200.75,
            change: -28.3,
            changePercent: -0.2,
            trend: 'down',
          },
          {
            name: 'Dow Jones',
            symbol: 'DJI',
            value: 34500.8,
            change: 175.2,
            changePercent: 0.51,
            trend: 'up',
          },
        ],
        commodities: [
          {
            name: '黄金',
            symbol: 'GOLD',
            value: 2000.5,
            change: 15.3,
            changePercent: 0.77,
            trend: 'up',
          },
          {
            name: '原油',
            symbol: 'OIL',
            value: 75.25,
            change: -1.2,
            changePercent: -1.57,
            trend: 'down',
          },
        ],
        currencies: [
          {
            name: '美元指数',
            symbol: 'DXY',
            value: 103.45,
            change: 0.25,
            changePercent: 0.24,
            trend: 'up',
          },
          {
            name: '美元人民币',
            symbol: 'USDCNY',
            value: 7.2,
            change: 0.02,
            changePercent: 0.28,
            trend: 'up',
          },
        ],
        marketSentiment: {
          fearGreedIndex: 65,
          sentiment: 'neutral',
          description: '市场情绪中性，投资者保持谨慎乐观',
        },
        lastUpdated: new Date().toISOString(),
      }

      res.json({
        success: true,
        data: mockOverview,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取市场概览失败:', error)
      res.status(500).json({
        success: false,
        message: '获取市场概览失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取市场分析
   */
  async getMarketAnalysis(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // 模拟市场分析数据
      const mockAnalysis = {
        summary: '当前市场处于震荡调整阶段，投资者关注美联储政策动向',
        keyPoints: [
          '美股三大指数表现分化，科技股承压',
          '债券收益率上升，市场流动性收紧',
          '大宗商品价格波动，通胀预期升温',
          '美元走强，新兴市场货币承压',
        ],
        risks: ['地缘政治风险持续', '通胀压力仍然存在', '货币政策收紧预期'],
        opportunities: [
          '价值股估值相对合理',
          '部分成长股出现超跌',
          '大宗商品长期需求支撑',
        ],
        recommendations: [
          '保持适度谨慎，关注政策变化',
          '均衡配置，降低单一资产风险',
          '关注高质量成长股的投资机会',
        ],
        generatedAt: new Date().toISOString(),
      }

      res.json({
        success: true,
        data: mockAnalysis,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取市场分析失败:', error)
      res.status(500).json({
        success: false,
        message: '获取市场分析失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const marketController = new MarketController()
