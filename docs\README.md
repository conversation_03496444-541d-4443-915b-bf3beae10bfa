# 项目文档目录

本目录包含 AI 驱动资产管理应用的详细技术文档。

## 📚 文档列表

### 架构设计

- [`architecture-design-document.md`](architecture-design-document.md) - 完整的系统架构设计文档
- [`system-architecture.md`](system-architecture.md) - 系统架构概述

### API 规范

- [`api-specification.md`](api-specification.md) - RESTful API 接口规范文档

### 数据库设计

- [`database-design.md`](database-design.md) - 数据库结构设计和关系图

### 项目结构

- [`project-structure.md`](project-structure.md) - 项目文件结构说明

### 技术挑战

- [`technical-challenges.md`](technical-challenges.md) - 技术挑战分析和解决方案

## 📖 文档使用指南

### 新开发者入门

建议按以下顺序阅读文档：

1. [`architecture-design-document.md`](architecture-design-document.md) - 了解整体架构
2. [`project-structure.md`](project-structure.md) - 熟悉项目结构
3. [`database-design.md`](database-design.md) - 理解数据模型
4. [`api-specification.md`](api-specification.md) - 掌握 API 接口

### 运维人员

重点关注：

- [`architecture-design-document.md`](architecture-design-document.md) - 部署架构
- [`technical-challenges.md`](technical-challenges.md) - 潜在问题和解决方案

### 产品经理

建议阅读：

- [`architecture-design-document.md`](architecture-design-document.md) - 功能架构
- [`api-specification.md`](api-specification.md) - 功能接口

## 🔄 文档维护

- 文档与代码同步更新
- 重大架构变更需要更新相关文档
- 新功能开发需要更新 API 规范

## 📞 联系方式

如有文档相关问题，请联系开发团队或在项目仓库创建 Issue。
