import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { User, UserSettings } from '../types'

interface UserState {
  user: User | null
  settings: UserSettings | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

interface UserActions {
  setUser: (user: User) => void
  setSettings: (settings: UserSettings) => void
  login: (user: User) => void
  logout: () => void
  setLoading: (loading: boolean) => void
  setError: (error: string | null) => void
  clearError: () => void
}

type UserStore = UserState & UserActions

export const useUserStore = create<UserStore>()(
  persist(
    (set) => ({
      // State
      user: null,
      settings: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      setUser: (user) => set({ user }),

      setSettings: (settings) => set({ settings }),

      login: (user) =>
        set({
          user,
          isAuthenticated: true,
          error: null,
        }),

      logout: () =>
        set({
          user: null,
          settings: null,
          isAuthenticated: false,
          error: null,
        }),

      setLoading: (isLoading) => set({ isLoading }),

      setError: (error) => set({ error }),

      clearError: () => set({ error: null }),
    }),
    {
      name: 'user-storage',
      partialize: (state) => ({
        user: state.user,
        settings: state.settings,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
)
