#!/bin/bash

# AI驱动资产管理应用部署脚本
# 支持开发环境和生产环境部署

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查系统要求..."
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装。请安装 Node.js 22+ 版本"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        log_error "Node.js 版本过低。需要 18+ 版本，当前版本: $(node -v)"
        exit 1
    fi
    
    # 检查pnpm
    if ! command -v pnpm &> /dev/null; then
        log_warning "pnpm 未安装，正在安装..."
        npm install -g pnpm
    fi
    
    # 检查Docker（可选）
    if command -v docker &> /dev/null; then
        log_success "Docker 已安装"
        DOCKER_AVAILABLE=true
    else
        log_warning "Docker 未安装，将使用本地部署模式"
        DOCKER_AVAILABLE=false
    fi
    
    log_success "系统要求检查完成"
}

# 环境配置
setup_environment() {
    local env_type=$1
    log_info "配置 $env_type 环境..."
    
    # 复制环境变量文件
    if [ ! -f "backend/.env" ]; then
        cp backend/.env.example backend/.env
        log_warning "已创建 backend/.env 文件，请根据需要修改配置"
    fi
    
    if [ ! -f "frontend/.env" ]; then
        if [ -f "frontend/.env.example" ]; then
            cp frontend/.env.example frontend/.env
            log_warning "已创建 frontend/.env 文件，请根据需要修改配置"
        fi
    fi
    
    # 创建数据目录
    mkdir -p data logs
    
    log_success "环境配置完成"
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    # 安装根目录依赖
    log_info "安装根目录依赖..."
    pnpm install
    
    # 安装后端依赖
    log_info "安装后端依赖..."
    cd backend
    pnpm install
    cd ..
    
    # 安装前端依赖
    log_info "安装前端依赖..."
    cd frontend
    pnpm install
    cd ..
    
    log_success "依赖安装完成"
}

# 构建项目
build_project() {
    log_info "构建项目..."
    
    # 构建后端
    log_info "构建后端..."
    cd backend
    pnpm run build
    cd ..
    
    # 构建前端
    log_info "构建前端..."
    cd frontend
    pnpm run build
    cd ..
    
    log_success "项目构建完成"
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    cd backend
    
    # 运行数据库迁移
    if [ -f "package.json" ] && grep -q "db:migrate" package.json; then
        pnpm run db:migrate
    else
        log_warning "未找到数据库迁移脚本，跳过迁移"
    fi
    
    # 插入种子数据
    if [ -f "package.json" ] && grep -q "db:seed" package.json; then
        pnpm run db:seed
    else
        log_warning "未找到数据库种子脚本，跳过种子数据插入"
    fi
    
    cd ..
    
    log_success "数据库初始化完成"
}

# Docker部署
deploy_docker() {
    log_info "使用Docker部署..."
    
    # 检查docker-compose文件
    if [ ! -f "docker-compose.yml" ]; then
        log_error "docker-compose.yml 文件不存在"
        exit 1
    fi
    
    # 构建并启动容器
    docker-compose down --remove-orphans
    docker-compose build --no-cache
    docker-compose up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "Docker部署成功"
        log_info "前端访问地址: http://localhost:3000"
        log_info "后端API地址: http://localhost:3001"
        log_info "查看日志: docker-compose logs -f"
    else
        log_error "Docker部署失败"
        docker-compose logs
        exit 1
    fi
}

# 本地部署
deploy_local() {
    log_info "使用本地模式部署..."
    
    # 初始化数据库
    init_database
    
    # 创建启动脚本
    cat > start.sh << 'EOF'
#!/bin/bash

# 启动后端服务
echo "启动后端服务..."
cd backend
pnpm run start &
BACKEND_PID=$!
cd ..

# 启动前端服务
echo "启动前端服务..."
cd frontend
pnpm run preview &
FRONTEND_PID=$!
cd ..

echo "服务已启动"
echo "前端访问地址: http://localhost:4173"
echo "后端API地址: http://localhost:3001"
echo "按 Ctrl+C 停止服务"

# 等待中断信号
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF
    
    chmod +x start.sh
    
    log_success "本地部署准备完成"
    log_info "运行 './start.sh' 启动服务"
}

# 开发环境部署
deploy_development() {
    log_info "部署开发环境..."
    
    setup_environment "development"
    install_dependencies
    init_database
    
    # 创建开发启动脚本
    cat > dev.sh << 'EOF'
#!/bin/bash

# 启动开发服务
echo "启动开发环境..."

# 启动后端开发服务
cd backend
pnpm run dev &
BACKEND_PID=$!
cd ..

# 启动前端开发服务
cd frontend
pnpm run dev &
FRONTEND_PID=$!
cd ..

echo "开发环境已启动"
echo "前端开发地址: http://localhost:5173"
echo "后端API地址: http://localhost:3001"
echo "按 Ctrl+C 停止服务"

# 等待中断信号
trap "kill $BACKEND_PID $FRONTEND_PID; exit" INT
wait
EOF
    
    chmod +x dev.sh
    
    log_success "开发环境部署完成"
    log_info "运行 './dev.sh' 启动开发服务"
}

# 生产环境部署
deploy_production() {
    log_info "部署生产环境..."
    
    setup_environment "production"
    install_dependencies
    build_project
    
    if [ "$DOCKER_AVAILABLE" = true ]; then
        deploy_docker
    else
        deploy_local
    fi
}

# 健康检查
health_check() {
    log_info "执行健康检查..."
    
    # 检查后端服务
    if curl -f http://localhost:3001/api/v1/health > /dev/null 2>&1; then
        log_success "后端服务运行正常"
    else
        log_warning "后端服务可能未启动或存在问题"
    fi
    
    # 检查前端服务
    if curl -f http://localhost:3000 > /dev/null 2>&1 || curl -f http://localhost:4173 > /dev/null 2>&1 || curl -f http://localhost:5173 > /dev/null 2>&1; then
        log_success "前端服务运行正常"
    else
        log_warning "前端服务可能未启动或存在问题"
    fi
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    
    # 停止Docker容器
    if [ "$DOCKER_AVAILABLE" = true ]; then
        docker-compose down
    fi
    
    # 清理构建文件
    rm -rf backend/dist
    rm -rf frontend/dist
    
    log_success "清理完成"
}

# 显示帮助信息
show_help() {
    echo "AI驱动资产管理应用部署脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  dev         部署开发环境"
    echo "  prod        部署生产环境"
    echo "  docker      使用Docker部署"
    echo "  local       本地部署"
    echo "  build       仅构建项目"
    echo "  install     仅安装依赖"
    echo "  health      健康检查"
    echo "  clean       清理项目"
    echo "  help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 dev      # 部署开发环境"
    echo "  $0 prod     # 部署生产环境"
    echo "  $0 docker   # 使用Docker部署"
}

# 主函数
main() {
    local command=${1:-help}
    
    case $command in
        "dev"|"development")
            check_requirements
            deploy_development
            ;;
        "prod"|"production")
            check_requirements
            deploy_production
            ;;
        "docker")
            check_requirements
            setup_environment "production"
            install_dependencies
            build_project
            deploy_docker
            ;;
        "local")
            check_requirements
            setup_environment "production"
            install_dependencies
            build_project
            deploy_local
            ;;
        "build")
            check_requirements
            install_dependencies
            build_project
            ;;
        "install")
            check_requirements
            install_dependencies
            ;;
        "health")
            health_check
            ;;
        "clean")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"