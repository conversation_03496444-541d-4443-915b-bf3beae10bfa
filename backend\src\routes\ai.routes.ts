import { Router } from 'express'
import { aiController } from '../controllers/aiController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 应用认证中间件（单用户模式）
router.use(authMiddleware)

// 获取可用的AI模型
router.get('/models', asyncHandler(aiController.getAvailableModels))

// 获取用户对话列表
router.get('/conversations', asyncHandler(aiController.getUserConversations))

// 创建新对话
router.post('/conversations', asyncHandler(aiController.createConversation))

// 获取对话消息
router.get(
  '/conversations/:conversationId/messages',
  asyncHandler(aiController.getConversationMessages)
)

// 发送消息并获取AI回复
router.post(
  '/conversations/:conversationId/messages',
  asyncHand<PERSON>(aiController.sendMessage)
)

// 流式发送消息
router.post(
  '/conversations/:conversationId/messages/stream',
  asyncHandler(aiController.sendMessageStream)
)

// 更新对话标题
router.put(
  '/conversations/:conversationId/title',
  asyncHandler(aiController.updateConversationTitle)
)

// 删除对话
router.delete(
  '/conversations/:conversationId',
  asyncHandler(aiController.deleteConversation)
)

// 获取AI使用统计
router.get('/usage/stats', asyncHandler(aiController.getAIUsageStats))

// 工作流相关路由
// 执行完整的AI代理工作流
router.post(
  '/conversations/:conversationId/workflow',
  asyncHandler(aiController.executeWorkflow)
)

// 执行单个AI代理
router.post(
  '/conversations/:conversationId/agents/:agentName',
  asyncHandler(aiController.executeSingleAgent)
)

// 获取工作流状态
router.get('/workflow/status', asyncHandler(aiController.getWorkflowStatus))

// 获取工作流执行历史
router.get('/workflow/history', asyncHandler(aiController.getWorkflowHistory))

// 重置工作流
router.post('/workflow/reset', asyncHandler(aiController.resetWorkflow))

// 工作流健康检查
router.get('/workflow/health', asyncHandler(aiController.workflowHealthCheck))

export default router
