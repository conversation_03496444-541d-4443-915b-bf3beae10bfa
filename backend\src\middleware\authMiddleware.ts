import { Request, Response, NextFunction } from 'express'
import { logger } from '../utils/logger'

// 单用户模式 - 不需要认证
export const authMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // 为所有请求添加默认用户信息
  ;(req as any).user = {
    id: 'default-user',
    email: 'user@localhost',
    name: '用户',
  }

  next()
}

// 可选的认证中间件（如果将来需要启用认证）
export const optionalAuthMiddleware = (
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // 单用户模式下直接通过
  ;(req as any).user = {
    id: 'default-user',
    email: 'user@localhost',
    name: '用户',
  }

  next()
}
