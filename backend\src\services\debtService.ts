import { eq, desc, and, sql } from 'drizzle-orm'
import { v4 as uuidv4 } from 'uuid'
import { db } from '../database/connection'
import { debts, debtTypes, debtPayments } from '../database/schema'
import { logger } from '../utils/logger'
import type { Debt, DebtType, DebtPayment } from '../types'

export class DebtService {
  /**
   * 获取所有负债
   */
  async getAllDebts(): Promise<Debt[]> {
    try {
      const allDebts = await db().select().from(debts).orderBy(desc(debts.updatedAt))

      logger.info(`获取负债成功, 数量: ${allDebts.length}`)
      return allDebts
    } catch (error) {
      logger.error('获取负债失败:', error)
      throw error
    }
  }

  /**
   * 根据类型获取负债
   */
  async getDebtsByType(typeId: string): Promise<Debt[]> {
    try {
      const typeDebts = await db()
        .select()
        .from(debts)
        .where(eq(debts.typeId, typeId))
        .orderBy(desc(debts.updatedAt))

      return typeDebts
    } catch (error) {
      logger.error('根据类型获取负债失败:', error)
      throw error
    }
  }

  /**
   * 根据ID获取单个负债
   */
  async getDebtById(id: string): Promise<Debt | null> {
    try {
      const debt = await db().select().from(debts).where(eq(debts.id, id)).limit(1)

      return debt[0] || null
    } catch (error) {
      logger.error('根据ID获取负债失败:', error)
      throw error
    }
  }

  /**
   * 创建新负债
   */
  async createDebt(
    debtData: Omit<Debt, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Debt> {
    try {
      // 验证负债类型是否存在
      const typeExists = await db()
        .select({ id: debtTypes.id })
        .from(debtTypes)
        .where(eq(debtTypes.id, debtData.typeId))
        .limit(1)

      if (!typeExists.length) {
        throw new Error(`负债类型不存在: ${debtData.typeId}`)
      }

      const newDebt = await db()
        .insert(debts)
        .values({
          id: uuidv4(),
          ...debtData,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning()

      if (!newDebt[0]) {
        throw new Error('负债创建失败')
      }

      logger.info(`负债创建成功: ${newDebt[0].id}`)
      return newDebt[0]
    } catch (error) {
      logger.error('创建负债失败:', error)
      throw error
    }
  }

  /**
   * 更新负债
   */
  async updateDebt(
    id: string,
    updateData: Partial<Omit<Debt, 'id' | 'createdAt' | 'updatedAt'>>
  ): Promise<Debt> {
    try {
      // 检查负债是否存在
      const existingDebt = await this.getDebtById(id)
      if (!existingDebt) {
        throw new Error(`负债不存在: ${id}`)
      }

      // 如果更新了类型，验证新类型是否存在
      if (updateData.typeId) {
        const typeExists = await db()
          .select({ id: debtTypes.id })
          .from(debtTypes)
          .where(eq(debtTypes.id, updateData.typeId))
          .limit(1)

        if (!typeExists.length) {
          throw new Error(`负债类型不存在: ${updateData.typeId}`)
        }
      }

      const updatedDebt = await db()
        .update(debts)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(debts.id, id))
        .returning()

      if (!updatedDebt[0]) {
        throw new Error('负债更新失败')
      }

      logger.info(`负债更新成功: ${id}`)
      return updatedDebt[0]
    } catch (error) {
      logger.error('更新负债失败:', error)
      throw error
    }
  }

  /**
   * 删除负债
   */
  async deleteDebt(id: string): Promise<void> {
    try {
      // 检查负债是否存在
      const existingDebt = await this.getDebtById(id)
      if (!existingDebt) {
        throw new Error(`负债不存在: ${id}`)
      }

      await db().delete(debts).where(eq(debts.id, id))

      logger.info(`负债删除成功: ${id}`)
    } catch (error) {
      logger.error('删除负债失败:', error)
      throw error
    }
  }

  /**
   * 获取负债统计信息
   */
  async getDebtStatistics(): Promise<{
    totalDebts: number
    totalBalance: number
    totalMonthlyPayment: number
    debtsByType: Array<{
      typeId: string
      typeName: string
      count: number
      totalBalance: number
      averageInterestRate: number
    }>
  }> {
    try {
      // 获取总体统计
      const totalStats = await db()
        .select({
          count: sql<number>`count(*)`,
          totalBalance: sql<number>`sum(current_balance)`,
          totalMonthlyPayment: sql<number>`sum(monthly_payment)`,
        })
        .from(debts)

      // 按类型统计
      const typeStats = await db()
        .select({
          typeId: debts.typeId,
          typeName: debtTypes.name,
          count: sql<number>`count(*)`,
          totalBalance: sql<number>`sum(current_balance)`,
          averageInterestRate: sql<number>`avg(interest_rate)`,
        })
        .from(debts)
        .leftJoin(debtTypes, eq(debts.typeId, debtTypes.id))
        .groupBy(debts.typeId, debtTypes.name)

      return {
        totalDebts: totalStats[0]?.count || 0,
        totalBalance: totalStats[0]?.totalBalance || 0,
        totalMonthlyPayment: totalStats[0]?.totalMonthlyPayment || 0,
        debtsByType: typeStats.map(stat => ({
          typeId: stat.typeId,
          typeName: stat.typeName || '未知类型',
          count: stat.count,
          totalBalance: stat.totalBalance,
          averageInterestRate: stat.averageInterestRate,
        })),
      }
    } catch (error) {
      logger.error('获取负债统计失败:', error)
      throw error
    }
  }

  /**
   * 获取所有负债类型
   */
  async getAllDebtTypes(): Promise<DebtType[]> {
    try {
      const allTypes = await db().select().from(debtTypes)
      return allTypes
    } catch (error) {
      logger.error('获取负债类型失败:', error)
      throw error
    }
  }

  /**
   * 创建负债类型
   */
  async createDebtType(
    typeData: Omit<DebtType, 'id'>
  ): Promise<DebtType> {
    try {
      const newType = await db()
        .insert(debtTypes)
        .values({
          id: uuidv4(),
          ...typeData,
        })
        .returning()

      if (!newType[0]) {
        throw new Error('负债类型创建失败')
      }

      logger.info(`负债类型创建成功: ${newType[0].id}`)
      return newType[0]
    } catch (error) {
      logger.error('创建负债类型失败:', error)
      throw error
    }
  }

  /**
   * 搜索负债
   */
  async searchDebts(query: string): Promise<Debt[]> {
    try {
      const searchResults = await db()
        .select()
        .from(debts)
        .where(sql`name LIKE ${`%${query}%`}`)
        .orderBy(desc(debts.updatedAt))

      return searchResults
    } catch (error) {
      logger.error('搜索负债失败:', error)
      throw error
    }
  }

  /**
   * 计算负债健康度评分
   */
  calculateDebtHealthScore(debts: Debt[]): {
    score: number
    level: 'excellent' | 'good' | 'fair' | 'poor'
    factors: Array<{
      name: string
      score: number
      weight: number
      description: string
    }>
  } {
    if (debts.length === 0) {
      return {
        score: 100,
        level: 'excellent',
        factors: [],
      }
    }

    const factors = []
    let totalScore = 0
    let totalWeight = 0

    // 因子1: 负债总额占比 (权重: 30%)
    const totalBalance = debts.reduce((sum, debt) => sum + debt.currentBalance, 0)
    const debtRatioScore = Math.max(0, 100 - (totalBalance / 1000000) * 50) // 假设100万为基准
    factors.push({
      name: '负债总额',
      score: debtRatioScore,
      weight: 0.3,
      description: '负债总额相对合理范围的评分',
    })
    totalScore += debtRatioScore * 0.3
    totalWeight += 0.3

    // 因子2: 平均利率 (权重: 25%)
    const avgInterestRate = debts.reduce((sum, debt) => sum + debt.interestRate, 0) / debts.length
    const interestRateScore = Math.max(0, 100 - avgInterestRate * 1000) // 利率越低越好
    factors.push({
      name: '平均利率',
      score: interestRateScore,
      weight: 0.25,
      description: '负债平均利率水平评分',
    })
    totalScore += interestRateScore * 0.25
    totalWeight += 0.25

    // 因子3: 负债多样性 (权重: 20%)
    const uniqueTypes = new Set(debts.map(debt => debt.typeId)).size
    const diversityScore = Math.min(100, uniqueTypes * 25) // 类型越多样化越好，但有上限
    factors.push({
      name: '负债多样性',
      score: diversityScore,
      weight: 0.2,
      description: '负债类型分散程度评分',
    })
    totalScore += diversityScore * 0.2
    totalWeight += 0.2

    // 因子4: 还款能力 (权重: 25%)
    const totalMonthlyPayment = debts.reduce((sum, debt) => sum + (debt.monthlyPayment || 0), 0)
    const paymentCapacityScore = totalMonthlyPayment > 0 ? Math.min(100, (50000 / totalMonthlyPayment) * 100) : 50
    factors.push({
      name: '还款能力',
      score: paymentCapacityScore,
      weight: 0.25,
      description: '月还款额相对收入的合理性评分',
    })
    totalScore += paymentCapacityScore * 0.25
    totalWeight += 0.25

    const finalScore = totalWeight > 0 ? totalScore / totalWeight : 0

    let level: 'excellent' | 'good' | 'fair' | 'poor'
    if (finalScore >= 80) level = 'excellent'
    else if (finalScore >= 60) level = 'good'
    else if (finalScore >= 40) level = 'fair'
    else level = 'poor'

    return {
      score: Math.round(finalScore),
      level,
      factors,
    }
  }
}

  /**
   * 添加还款记录
   */
  async addPayment(
    debtId: string,
    paymentData: Omit<DebtPayment, 'id' | 'debtId' | 'createdAt'>
  ): Promise<DebtPayment> {
    try {
      // 检查负债是否存在
      const existingDebt = await this.getDebtById(debtId)
      if (!existingDebt) {
        throw new Error(`负债不存在: ${debtId}`)
      }

      const newPayment = await db()
        .insert(debtPayments)
        .values({
          id: uuidv4(),
          debtId,
          ...paymentData,
          createdAt: new Date(),
        })
        .returning()

      if (!newPayment[0]) {
        throw new Error('还款记录创建失败')
      }

      // 更新负债余额
      const newBalance = existingDebt.currentBalance - paymentData.principalAmount
      await this.updateDebt(debtId, { currentBalance: Math.max(0, newBalance) })

      logger.info(`还款记录创建成功: ${newPayment[0].id}`)
      return newPayment[0]
    } catch (error) {
      logger.error('添加还款记录失败:', error)
      throw error
    }
  }

  /**
   * 获取负债的还款记录
   */
  async getDebtPayments(debtId: string): Promise<DebtPayment[]> {
    try {
      const payments = await db()
        .select()
        .from(debtPayments)
        .where(eq(debtPayments.debtId, debtId))
        .orderBy(desc(debtPayments.paymentDate))

      return payments
    } catch (error) {
      logger.error('获取还款记录失败:', error)
      throw error
    }
  }

  /**
   * 删除还款记录
   */
  async deletePayment(paymentId: string): Promise<void> {
    try {
      // 获取还款记录
      const payment = await db()
        .select()
        .from(debtPayments)
        .where(eq(debtPayments.id, paymentId))
        .limit(1)

      if (!payment[0]) {
        throw new Error(`还款记录不存在: ${paymentId}`)
      }

      // 获取对应的负债
      const debt = await this.getDebtById(payment[0].debtId)
      if (debt) {
        // 恢复负债余额
        const newBalance = debt.currentBalance + payment[0].principalAmount
        await this.updateDebt(debt.id, { currentBalance: newBalance })
      }

      // 删除还款记录
      await db().delete(debtPayments).where(eq(debtPayments.id, paymentId))

      logger.info(`还款记录删除成功: ${paymentId}`)
    } catch (error) {
      logger.error('删除还款记录失败:', error)
      throw error
    }
  }

  /**
   * 获取还款统计
   */
  async getPaymentStatistics(debtId?: string): Promise<{
    totalPayments: number
    totalAmount: number
    totalPrincipal: number
    totalInterest: number
    averagePayment: number
    lastPaymentDate: string | null
  }> {
    try {
      let query = db()
        .select({
          count: sql<number>`count(*)`,
          totalAmount: sql<number>`sum(amount)`,
          totalPrincipal: sql<number>`sum(principal_amount)`,
          totalInterest: sql<number>`sum(interest_amount)`,
          lastPaymentDate: sql<string>`max(payment_date)`,
        })
        .from(debtPayments)

      if (debtId) {
        query = query.where(eq(debtPayments.debtId, debtId))
      }

      const stats = await query

      const result = stats[0]
      return {
        totalPayments: result?.count || 0,
        totalAmount: result?.totalAmount || 0,
        totalPrincipal: result?.totalPrincipal || 0,
        totalInterest: result?.totalInterest || 0,
        averagePayment: result?.count > 0 ? (result?.totalAmount || 0) / result.count : 0,
        lastPaymentDate: result?.lastPaymentDate || null,
      }
    } catch (error) {
      logger.error('获取还款统计失败:', error)
      throw error
    }
  }
}

// 导出单例实例
export const debtService = new DebtService()
