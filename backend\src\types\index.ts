import { Request } from 'express'

// 基础响应类型
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

// 分页响应类型
export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

// 扩展的Request类型，包含用户信息
export interface AuthenticatedRequest extends Request {
  user: {
    id: string
    email: string
    name: string
  }
  body: any
  params: any
  query: any
}

// 用户相关类型
export interface User {
  id: string
  username: string
  email: string
  password: string
  fullName?: string
  avatar?: string
  createdAt: Date
  updatedAt: Date
  lastLoginAt?: Date
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  fullName?: string
  avatar?: string
}

export interface LoginRequest {
  email: string
  password: string
}

export interface UpdateUserRequest {
  username?: string
  fullName?: string
  avatar?: string
}

export interface UserSettings {
  id: string
  userId: string
  aiApiKeys: Record<string, string> // 加密存储的API Keys
  preferredCurrency: string
  timezone: string
  dashboardLayout: any
  updatedAt: Date
}

// 资产相关类型
export interface Asset {
  id: string
  categoryId: string
  name: string
  description: string | null
  currentValue: number
  purchasePrice: number | null
  purchaseDate: string | null
  currency: string | null
  metadata: string | null
  createdAt: Date
  updatedAt: Date
}

export interface AssetCategory {
  id: string
  name: string
  description: string | null
  icon: string | null
  color: string | null
}

export interface AssetHistory {
  id: string
  assetId: string
  value: number
  recordDate: string
  createdAt: Date
}

// 负债相关类型
export interface Debt {
  id: string
  userId: string
  typeId: string
  name: string
  principalAmount: number
  currentBalance: number
  interestRate: number
  startDate: string
  monthlyPayment?: number
  currency: string
  createdAt: Date
  updatedAt: Date
}

export interface DebtType {
  id: string
  name: string
  description?: string
  icon: string
  color: string
}

export interface DebtPayment {
  id: string
  debtId: string
  amount: number
  principalAmount: number
  interestAmount: number
  paymentDate: string
  createdAt: Date
}

// AI相关类型
export interface AIModel {
  id: string
  name: string
  provider: string
  modelId: string
  costPerToken: number | null
  isActive: boolean | null
  createdAt: Date
}

export interface AIConversation {
  id: string
  userId: string
  title: string
  contextType: string | null
  contextData: string | null
  createdAt: Date
  updatedAt: Date
}

export interface AIMessage {
  id: string
  conversationId: string
  modelId: string
  role: string
  content: string
  metadata: string | null
  tokensUsed: number | null
  cost: number | null
  createdAt: Date
}

// 新闻相关类型
export interface News {
  id: string
  title: string
  content?: string
  summary?: string
  url?: string
  source: string
  publishedDate: string
  relevanceScore?: number
  createdAt: Date
}

// 市场数据相关类型
export interface MarketIndicator {
  id: string
  name: string
  symbol: string
  description?: string
  category: string
  isActive: boolean
  createdAt: Date
}

export interface MarketData {
  id: string
  indicatorId: string
  value: number
  changeAmount?: number
  changePercentage?: number
  recordDate: string
  createdAt: Date
}

// 日历事件类型
export interface CalendarEvent {
  id: string
  userId: string
  title: string
  description?: string
  eventType: string
  startTime: Date
  endTime?: Date
  isRecurring: boolean
  recurrenceRule?: any
  createdAt: Date
  updatedAt: Date
}

// 数据更新任务类型
export interface DataUpdateTask {
  id: string
  type: 'market' | 'news'
  status: 'started' | 'running' | 'completed' | 'failed'
  progress: number
  message: string
  updatedCount?: number
  errors?: string[]
  createdAt: Date
  updatedAt: Date
}

// JWT Payload类型
export interface JWTPayload {
  userId: string
  email: string
  name: string
  iat: number
  exp: number
}

// 错误类型
export class AppError extends Error {
  public statusCode: number
  public code: string
  public isOperational: boolean

  constructor(
    message: string,
    statusCode: number = 500,
    code: string = 'INTERNAL_ERROR'
  ) {
    super(message)
    this.statusCode = statusCode
    this.code = code
    this.isOperational = true

    Error.captureStackTrace(this, this.constructor)
  }
}

// 验证错误类型
export class ValidationError extends AppError {
  constructor(message: string, details?: any) {
    super(message, 400, 'VALIDATION_ERROR')
    this.name = 'ValidationError'
    if (details) {
      ;(this as any).details = details
    }
  }
}

// 认证错误类型
export class AuthenticationError extends AppError {
  constructor(message: string = '未授权访问') {
    super(message, 401, 'UNAUTHORIZED')
    this.name = 'AuthenticationError'
  }
}

// 权限错误类型
export class AuthorizationError extends AppError {
  constructor(message: string = '权限不足') {
    super(message, 403, 'FORBIDDEN')
    this.name = 'AuthorizationError'
  }
}

// 资源未找到错误类型
export class NotFoundError extends AppError {
  constructor(message: string = '资源不存在') {
    super(message, 404, 'NOT_FOUND')
    this.name = 'NotFoundError'
  }
}

// 冲突错误类型
export class ConflictError extends AppError {
  constructor(message: string = '资源冲突') {
    super(message, 409, 'CONFLICT')
    this.name = 'ConflictError'
  }
}
