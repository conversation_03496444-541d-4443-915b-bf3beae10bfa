import { sqliteTable, text, integer, real } from 'drizzle-orm/sqlite-core'
import { relations } from 'drizzle-orm'

// 应用设置表（单用户模式）
export const appSettings = sqliteTable('app_settings', {
  id: text('id').primaryKey(),
  aiApiKeys: text('ai_api_keys'), // JSON格式存储加密的API Keys
  preferredCurrency: text('preferred_currency').default('USD'),
  timezone: text('timezone').default('UTC'),
  dashboardLayout: text('dashboard_layout'), // JSON格式
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// 资产分类表
export const assetCategories = sqliteTable('asset_categories', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  icon: text('icon'),
  color: text('color'),
})

// 资产表
export const assets = sqliteTable('assets', {
  id: text('id').primaryKey(),
  categoryId: text('category_id')
    .notNull()
    .references(() => assetCategories.id),
  name: text('name').notNull(),
  description: text('description'),
  currentValue: real('current_value').notNull(),
  purchasePrice: real('purchase_price'),
  purchaseDate: text('purchase_date'),
  currency: text('currency').default('USD'),
  metadata: text('metadata'), // JSON格式
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// 资产历史表
export const assetHistory = sqliteTable('asset_history', {
  id: text('id').primaryKey(),
  assetId: text('asset_id')
    .notNull()
    .references(() => assets.id, { onDelete: 'cascade' }),
  value: real('value').notNull(),
  recordDate: text('record_date').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// 负债类型表
export const debtTypes = sqliteTable('debt_types', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  description: text('description'),
  icon: text('icon'),
  color: text('color'),
})

// 负债表
export const debts = sqliteTable('debts', {
  id: text('id').primaryKey(),
  userId: text('user_id').notNull(),
  typeId: text('type_id')
    .notNull()
    .references(() => debtTypes.id),
  name: text('name').notNull(),
  principalAmount: real('principal_amount').notNull(),
  currentBalance: real('current_balance').notNull(),
  interestRate: real('interest_rate').notNull(),
  startDate: text('start_date').notNull(),
  monthlyPayment: real('monthly_payment'),
  currency: text('currency').default('USD'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// 还款记录表
export const debtPayments = sqliteTable('debt_payments', {
  id: text('id').primaryKey(),
  debtId: text('debt_id')
    .notNull()
    .references(() => debts.id, { onDelete: 'cascade' }),
  amount: real('amount').notNull(),
  principalAmount: real('principal_amount').notNull(),
  interestAmount: real('interest_amount').notNull(),
  paymentDate: text('payment_date').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// AI模型表
export const aiModels = sqliteTable('ai_models', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  provider: text('provider').notNull(),
  modelId: text('model_id').notNull(),
  costPerToken: real('cost_per_token'),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// AI对话表
export const aiConversations = sqliteTable('ai_conversations', {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  contextType: text('context_type'),
  contextData: text('context_data'), // JSON格式
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// AI消息表
export const aiMessages = sqliteTable('ai_messages', {
  id: text('id').primaryKey(),
  conversationId: text('conversation_id')
    .notNull()
    .references(() => aiConversations.id, { onDelete: 'cascade' }),
  modelId: text('model_id')
    .notNull()
    .references(() => aiModels.id),
  role: text('role').notNull(), // 'user', 'assistant', 'system'
  content: text('content').notNull(),
  metadata: text('metadata'), // JSON格式
  tokensUsed: integer('tokens_used'),
  cost: real('cost'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// 新闻表
export const news = sqliteTable('news', {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  content: text('content'),
  summary: text('summary'),
  url: text('url'),
  source: text('source'),
  publishedDate: text('published_date'),
  relevanceScore: real('relevance_score'),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// 市场指标表
export const marketIndicators = sqliteTable('market_indicators', {
  id: text('id').primaryKey(),
  name: text('name').notNull(),
  symbol: text('symbol').notNull(),
  description: text('description'),
  category: text('category'),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// 市场数据表
export const marketData = sqliteTable('market_data', {
  id: text('id').primaryKey(),
  indicatorId: text('indicator_id')
    .notNull()
    .references(() => marketIndicators.id),
  value: real('value').notNull(),
  changeAmount: real('change_amount'),
  changePercentage: real('change_percentage'),
  recordDate: text('record_date').notNull(),
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
})

// 日历事件表
export const calendarEvents = sqliteTable('calendar_events', {
  id: text('id').primaryKey(),
  title: text('title').notNull(),
  description: text('description'),
  eventType: text('event_type'),
  startTime: integer('start_time', { mode: 'timestamp' }).notNull(),
  endTime: integer('end_time', { mode: 'timestamp' }),
  isRecurring: integer('is_recurring', { mode: 'boolean' }).default(false),
  recurrenceRule: text('recurrence_rule'), // JSON格式
  createdAt: integer('created_at', { mode: 'timestamp' }).notNull(),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).notNull(),
})

// 关系定义

export const assetsRelations = relations(assets, ({ one, many }) => ({
  category: one(assetCategories, {
    fields: [assets.categoryId],
    references: [assetCategories.id],
  }),
  history: many(assetHistory),
}))

export const assetCategoriesRelations = relations(assetCategories, ({ many }) => ({
  assets: many(assets),
}))

export const assetHistoryRelations = relations(assetHistory, ({ one }) => ({
  asset: one(assets, {
    fields: [assetHistory.assetId],
    references: [assets.id],
  }),
}))

export const debtsRelations = relations(debts, ({ one, many }) => ({
  type: one(debtTypes, {
    fields: [debts.typeId],
    references: [debtTypes.id],
  }),
  payments: many(debtPayments),
}))

export const debtTypesRelations = relations(debtTypes, ({ many }) => ({
  debts: many(debts),
}))

export const debtPaymentsRelations = relations(debtPayments, ({ one }) => ({
  debt: one(debts, {
    fields: [debtPayments.debtId],
    references: [debts.id],
  }),
}))

export const aiConversationsRelations = relations(aiConversations, ({ many }) => ({
  messages: many(aiMessages),
}))

export const aiMessagesRelations = relations(aiMessages, ({ one }) => ({
  conversation: one(aiConversations, {
    fields: [aiMessages.conversationId],
    references: [aiConversations.id],
  }),
  model: one(aiModels, {
    fields: [aiMessages.modelId],
    references: [aiModels.id],
  }),
}))

export const aiModelsRelations = relations(aiModels, ({ many }) => ({
  messages: many(aiMessages),
}))

export const marketIndicatorsRelations = relations(marketIndicators, ({ many }) => ({
  data: many(marketData),
}))

export const marketDataRelations = relations(marketData, ({ one }) => ({
  indicator: one(marketIndicators, {
    fields: [marketData.indicatorId],
    references: [marketIndicators.id],
  }),
}))

// Calendar events don't need relations in single-user mode
