import React, { useState, useEffect } from 'react'
import {
  Typography,
  Card,
  List,
  Tag,
  Input,
  Select,
  Row,
  Col,
  Spin,
  message,
  Button,
  Space,
  Statistic,
  Tabs,
  Empty,
} from 'antd'
import {
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  GlobalOutlined,
  DollarOutlined,
  FireOutlined,
  ClockCircleOutlined,
} from '@ant-design/icons'
import { newsService, type News } from '../../services/newsService'
import { marketService } from '../../services/marketService'

const { Title, Text, Paragraph } = Typography
const { Search } = Input
const { Option } = Select
const { TabPane } = Tabs

export const NewsPage: React.FC = () => {
  const [news, setNews] = useState<News[]>([])
  const [loading, setLoading] = useState(true)
  const [newsLoading, setNewsLoading] = useState(false)
  const [marketLoading, setMarketLoading] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  const categories = [
    { value: 'all', label: '全部' },
    { value: 'finance', label: '金融' },
    { value: 'stock', label: '股票' },
    { value: 'crypto', label: '数字货币' },
    { value: 'economy', label: '经济' },
    { value: 'policy', label: '政策' },
  ]

  useEffect(() => {
    loadInitialData()
  }, [])

  useEffect(() => {
    loadNews()
  }, [searchQuery, selectedCategory])

  const loadInitialData = async () => {
    setLoading(true)
    try {
      await loadNews()
    } catch (error) {
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  const loadNews = async () => {
    try {
      setNewsLoading(true)
      const params = {
        category: selectedCategory === 'all' ? undefined : selectedCategory,
        limit: 20,
      }

      const response = await newsService.getNews(params)
      if (response.success && response.data?.data) {
        setNews(response.data.data)
      }
    } catch (error) {
      message.error('加载新闻失败')
    } finally {
      setNewsLoading(false)
    }
  }

  const handleRefresh = async () => {
    await loadInitialData()
    message.success('数据已刷新')
  }

  const handleExportNews = async () => {
    message.info('导出功能开发中')
  }

  const formatTime = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)

    if (diffHours < 1) {
      return '刚刚'
    } else if (diffHours < 24) {
      return `${diffHours}小时前`
    } else if (diffDays < 7) {
      return `${diffDays}天前`
    } else {
      return date.toLocaleDateString('zh-CN')
    }
  }

  const getCategoryColor = (category: string) => {
    const colors: Record<string, string> = {
      finance: 'blue',
      stock: 'green',
      crypto: 'orange',
      economy: 'purple',
      policy: 'red',
    }
    return colors[category] || 'default'
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Title level={2} className="mb-0">
          新闻中心
        </Title>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            刷新
          </Button>
          <Button icon={<ExportOutlined />} onClick={handleExportNews}>
            导出
          </Button>
        </Space>
      </div>

      {/* 市场概览 */}
      <Card title="市场概览" loading={marketLoading}>
        <Tabs defaultActiveKey="indices">
          <TabPane tab="股指" key="indices">
            <Row gutter={[16, 16]}>
              {Array.from({ length: 6 }, (_, index) => (
                <Col xs={24} sm={12} lg={8} key={index}>
                  <Card size="small">
                    <Statistic
                      title={
                        [
                          '上证指数',
                          '深证成指',
                          '创业板指',
                          '恒生指数',
                          '纳斯达克',
                          '标普500',
                        ][index]
                      }
                      value={Math.random() * 1000 + 3000}
                      precision={2}
                      valueStyle={{
                        color:
                          index % 3 === 0
                            ? '#52c41a'
                            : index % 3 === 1
                            ? '#ff4d4f'
                            : '#1890ff',
                        fontSize: '16px',
                      }}
                      prefix={
                        index % 3 === 0 ? (
                          <ArrowUpOutlined />
                        ) : index % 3 === 1 ? (
                          <ArrowDownOutlined />
                        ) : (
                          <DollarOutlined />
                        )
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </TabPane>

          <TabPane tab="商品" key="commodities">
            <Row gutter={[16, 16]}>
              {Array.from({ length: 6 }, (_, index) => (
                <Col xs={24} sm={12} lg={8} key={index}>
                  <Card size="small">
                    <Statistic
                      title={['黄金', '原油', '白银', '铜', '天然气', '大豆'][index]}
                      value={Math.random() * 100 + 50}
                      precision={2}
                      valueStyle={{
                        color: index % 2 === 0 ? '#52c41a' : '#ff4d4f',
                        fontSize: '16px',
                      }}
                      prefix={
                        index % 2 === 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />
                      }
                      suffix="USD"
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </TabPane>

          <TabPane tab="汇率" key="currencies">
            <Row gutter={[16, 16]}>
              {Array.from({ length: 6 }, (_, index) => (
                <Col xs={24} sm={12} lg={8} key={index}>
                  <Card size="small">
                    <Statistic
                      title={
                        [
                          'USD/CNY',
                          'EUR/USD',
                          'GBP/USD',
                          'USD/JPY',
                          'AUD/USD',
                          'USD/CAD',
                        ][index]
                      }
                      value={Math.random() * 2 + 6}
                      precision={4}
                      valueStyle={{
                        color: index % 2 === 0 ? '#52c41a' : '#ff4d4f',
                        fontSize: '16px',
                      }}
                      prefix={
                        index % 2 === 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          </TabPane>
        </Tabs>
      </Card>

      {/* 新闻筛选 */}
      <Card>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={12} md={8}>
            <Search
              placeholder="搜索新闻..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onSearch={loadNews}
              enterButton={<SearchOutlined />}
            />
          </Col>
          <Col xs={12} sm={6} md={4}>
            <Select
              value={selectedCategory}
              onChange={setSelectedCategory}
              style={{ width: '100%' }}
              placeholder="选择分类"
            >
              {categories.map((cat) => (
                <Option key={cat.value} value={cat.value}>
                  {cat.label}
                </Option>
              ))}
            </Select>
          </Col>
        </Row>
      </Card>

      {/* 新闻列表 */}
      <Card title={`新闻资讯 (${news.length})`} loading={newsLoading}>
        {news.length > 0 ? (
          <List
            itemLayout="vertical"
            dataSource={news}
            renderItem={(article) => (
              <List.Item
                key={article.id}
                actions={[
                  <Space key="meta">
                    <ClockCircleOutlined />
                    <Text type="secondary">{formatTime(article.publishedDate)}</Text>
                  </Space>,
                  <Space key="source">
                    <GlobalOutlined />
                    <Text type="secondary">{article.source}</Text>
                  </Space>,
                  article.relevanceScore && article.relevanceScore >= 80 && (
                    <Tag key="hot" icon={<FireOutlined />} color="red">
                      热门
                    </Tag>
                  ),
                ]}
                extra={
                  <div className="flex flex-col items-end space-y-2">
                    <Tag color="blue">新闻</Tag>
                    {article.relevanceScore && (
                      <Tag
                        color={
                          article.relevanceScore >= 80
                            ? 'red'
                            : article.relevanceScore >= 60
                            ? 'orange'
                            : 'blue'
                        }
                      >
                        相关度: {article.relevanceScore}%
                      </Tag>
                    )}
                  </div>
                }
              >
                <List.Item.Meta
                  title={
                    <a
                      href={article.url || '#'}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-lg font-medium hover:text-blue-600"
                    >
                      {article.title}
                    </a>
                  }
                  description={
                    <div className="space-y-2">
                      <Paragraph
                        ellipsis={{ rows: 2, expandable: true, symbol: '展开' }}
                        className="text-gray-600"
                      >
                        {article.summary || article.content || '暂无摘要'}
                      </Paragraph>
                    </div>
                  }
                />
              </List.Item>
            )}
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) =>
                `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            }}
          />
        ) : (
          <Empty description="暂无新闻数据" image={Empty.PRESENTED_IMAGE_SIMPLE} />
        )}
      </Card>
    </div>
  )
}
