import { Router } from 'express'
import { settingsController } from '../controllers/settingsController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 获取支持的货币列表（无需认证）
router.get('/currencies', asyncHandler(settingsController.getSupportedCurrencies))

// 获取支持的时区列表（无需认证）
router.get('/timezones', asyncHandler(settingsController.getSupportedTimezones))

// 以下路由需要认证
router.use(authMiddleware)

// 获取用户设置
router.get('/', asyncHandler(settingsController.getUserSettings))

// 更新用户设置
router.put('/', asyncHandler(settingsController.updateUserSettings))

// 重置用户设置
router.post('/reset', asyncHandler(settingsController.resetUserSettings))

export default router
