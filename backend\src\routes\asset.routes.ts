import { Router } from 'express'
import { assetController } from '../controllers/assetController'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 获取资产分类（无需认证）
router.get('/categories', asyncHandler(assetController.getAssetCategories))

// 获取用户资产统计
router.get('/stats', asyncHandler(assetController.getUserAssetStats))

// 搜索资产
router.get('/search', asyncHandler(assetController.searchAssets))

// 批量更新资产价值
router.post('/batch-update', asyncHandler(assetController.batchUpdateAssetValues))

// 获取所有资产
router.get('/', asyncHandler(assetController.getAllAssets))

// 根据分类获取资产
router.get('/category/:categoryId', asyncHandler(assetController.getAssetsByCategory))

// 创建新资产
router.post('/', asyncHandler(assetController.createAsset))

// 获取单个资产详情
router.get('/:assetId', asyncHandler(assetController.getAssetById))

// 更新资产
router.put('/:assetId', asyncHandler(assetController.updateAsset))

// 删除资产
router.delete('/:assetId', asyncHandler(assetController.deleteAsset))

// 获取资产历史记录
router.get('/:assetId/history', asyncHandler(assetController.getAssetHistory))

// 添加资产历史记录
router.post('/:assetId/history', asyncHandler(assetController.addAssetHistory))

export default router
