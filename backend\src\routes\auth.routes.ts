import { Router } from 'express'
import { authController } from '../controllers/authController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 单用户模式的认证路由

// 获取用户状态（无需认证）
router.get('/status', asyncHandler(authController.getStatus))

// 初始化用户（无需认证）
router.post('/initialize', asyncHandler(authController.initialize))

// 获取当前用户信息
router.get('/me', authMiddleware, asyncHandler(authController.getCurrentUser))

// 更新用户信息
router.put('/profile', authMiddleware, asyncHandler(authController.updateProfile))

export default router
