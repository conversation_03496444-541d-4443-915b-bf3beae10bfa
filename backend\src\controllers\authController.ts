import { Request, Response } from 'express'
import { authService } from '../services/authService'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'

export class AuthController {
  /**
   * 获取当前用户信息（单用户模式）
   */
  async getCurrentUser(req: any, res: Response): Promise<void> {
    try {
      const user = await authService.getCurrentUser()

      res.json({
        success: true,
        data: user,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户信息失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户信息失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 更新用户信息
   */
  async updateProfile(req: any, res: Response): Promise<void> {
    try {
      const updateData = req.body

      // 移除不允许更新的字段
      delete updateData.id
      delete updateData.createdAt
      delete updateData.updatedAt

      const updatedUser = await authService.updateUser(updateData)

      res.json({
        success: true,
        data: updatedUser,
        message: '用户信息更新成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('更新用户信息失败:', error)
      res.status(500).json({
        success: false,
        message: '更新用户信息失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取用户状态（单用户模式下始终返回已登录状态）
   */
  async getStatus(req: Request, res: Response): Promise<void> {
    try {
      const user = await authService.getCurrentUser()

      res.json({
        success: true,
        data: {
          isLoggedIn: true,
          user: user,
        },
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户状态失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户状态失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 初始化用户（单用户模式）
   */
  async initialize(req: Request, res: Response): Promise<void> {
    try {
      await authService.initializeDefaultUser()
      const user = await authService.getCurrentUser()

      res.json({
        success: true,
        data: user,
        message: '用户初始化成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('用户初始化失败:', error)
      res.status(500).json({
        success: false,
        message: '用户初始化失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const authController = new AuthController()
