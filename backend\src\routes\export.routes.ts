import { Router, Request, Response } from 'express'
import { asyncHand<PERSON> } from '@/middleware/errorHandler'

const router: Router = Router()

router.get(
  '/database',
  asyncHandler(async (req: Request, res: Response) => {
    res.status(501).json({
      success: false,
      error: { code: 'NOT_IMPLEMENTED', message: '数据导出功能待实现' },
      timestamp: new Date().toISOString(),
    })
  })
)

export default router
