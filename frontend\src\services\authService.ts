import { apiClient } from './api'
import type { User } from '../types'

export interface AuthResponse {
  user: User
}

class AuthService {
  /**
   * 获取当前用户信息（单用户模式）
   */
  async getCurrentUser(): Promise<User> {
    const response = await apiClient.get<User>('/auth/me')
    return response.data!
  }

  /**
   * 更新用户信息
   */
  async updateProfile(userData: Partial<User>): Promise<User> {
    const response = await apiClient.put<User>('/auth/profile', userData)
    return response.data!
  }

  /**
   * 获取用户状态
   */
  async getStatus(): Promise<{ isLoggedIn: boolean; user: User }> {
    const response = await apiClient.get<{ isLoggedIn: boolean; user: User }>(
      '/auth/status'
    )
    return response.data!
  }

  /**
   * 初始化用户（首次访问时）
   */
  async initialize(): Promise<User> {
    const response = await apiClient.post<User>('/auth/initialize')
    return response.data!
  }

  /**
   * 检查是否已初始化（单用户模式下始终返回true）
   */
  isAuthenticated(): boolean {
    return true
  }

  /**
   * 获取用户ID（单用户模式）
   */
  getUserId(): string {
    return 'default-user'
  }
}

export const authService = new AuthService()
