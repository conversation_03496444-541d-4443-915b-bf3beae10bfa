# 财务分析AI代理系统

基于LangGraph StateGraph的综合财务分析AI代理系统，提供专业的财务分析、投资建议、风险评估和新闻分析服务。

## 系统架构

### 核心组件

1. **BaseAgent** - 所有代理的基础类
2. **FinancialAnalysisAgent** - 财务分析代理
3. **InvestmentAdvisorAgent** - 投资建议代理
4. **RiskAssessmentAgent** - 风险评估代理
5. **NewsAnalysisAgent** - 新闻分析代理
6. **CoordinatorAgent** - 协调代理
7. **FinancialWorkflowStateGraph** - 基于StateGraph的工作流引擎

### 工作流程

```
用户查询 → 财务分析 → 投资建议 → 风险评估 → 新闻分析 → 协调整合 → 综合建议
```

## 快速开始

### 基本使用

```typescript
import { createFinancialWorkflowInstance } from './agents'

// 创建工作流实例
const workflow = createFinancialWorkflowInstance('deepseek-chat')

// 执行分析
const result = await workflow.execute({
  userQuery: '请分析我的财务状况并提供投资建议',
  userId: 'user123',
  conversationId: 'conv456',
  modelId: 'deepseek-chat',
  messages: [],
  currentStep: 'start',
  nextSteps: [],
  isComplete: false,
  context: {},
  memory: {
    conversationHistory: [],
    userPreferences: {
      riskTolerance: 'moderate',
      investmentGoals: ['长期增值', '资产保值'],
      timeHorizon: '3-5年',
      preferredAssetTypes: ['股票', '债券'],
      excludedAssetTypes: ['加密货币'],
    },
    previousAnalyses: [],
    learnings: [],
  },
})

console.log('分析结果:', result)
```

### 单独使用代理

```typescript
import { createAgent } from './agents'

// 创建财务分析代理
const financialAgent = createAgent('financial', 'deepseek-chat')

// 执行分析
const state = {
  userId: 'user123',
  userQuery: '分析我的财务状况',
  // ... 其他状态字段
}

const updatedState = await financialAgent.execute(state)
```

## 代理详解

### 1. 财务分析代理 (FinancialAnalysisAgent)

**功能：**
- 分析用户资产负债情况
- 计算财务健康评分
- 分析现金流状况
- 评估资产配置合理性

**输出：**
- 总资产、总负债、净资产
- 财务健康评分
- 现金流分析
- 资产配置分析
- 改善建议

### 2. 投资建议代理 (InvestmentAdvisorAgent)

**功能：**
- 基于风险偏好提供投资建议
- 分析市场数据和趋势
- 制定资产配置策略
- 推荐具体投资产品

**输出：**
- 风险偏好评估
- 推荐资产配置
- 具体投资建议
- 预期收益估算

### 3. 风险评估代理 (RiskAssessmentAgent)

**功能：**
- 评估投资组合风险
- 分析风险因素
- 计算波动性指标
- 提供风险控制建议

**输出：**
- 整体风险等级
- 风险因素分析
- 多样化评分
- 波动性分析

### 4. 新闻分析代理 (NewsAnalysisAgent)

**功能：**
- 收集相关金融新闻
- 分析新闻对投资的影响
- 评估市场情绪
- 提供基于新闻的建议

**输出：**
- 相关新闻列表
- 市场情绪分析
- 影响分析
- 行动建议

### 5. 协调代理 (CoordinatorAgent)

**功能：**
- 整合各代理分析结果
- 解决建议冲突
- 提供综合建议
- 制定行动计划

**输出：**
- 综合分析报告
- 优先级建议
- 执行计划
- 监控要点

## 工具函数

系统提供了丰富的工具函数支持代理分析：

- `getUserAssets` - 获取用户资产
- `getUserDebts` - 获取用户负债
- `getUserProfile` - 获取用户档案
- `calculateTotalAssets` - 计算总资产
- `calculateTotalDebts` - 计算总负债
- `calculateNetWorth` - 计算净资产
- `analyzeAssetAllocation` - 分析资产配置
- `calculateFinancialHealthScore` - 计算财务健康评分
- `getMarketData` - 获取市场数据
- `getFinancialNews` - 获取金融新闻
- `calculatePortfolioRisk` - 计算投资组合风险
- `generateInvestmentRecommendations` - 生成投资建议

## 配置选项

### 支持的AI模型

- `deepseek-chat` (默认)
- `gpt-3.5-turbo`
- `gpt-4`
- `gpt-4-turbo`
- `claude-3-sonnet`
- `claude-3-opus`

### 默认配置

```typescript
{
  modelId: 'deepseek-chat',
  temperature: 0.3,
  maxIterations: 3,
  maxExecutionTime: 300000, // 5分钟
  maxRetries: 3,
}
```

## 健康检查

```typescript
import { validateAgentsHealth } from './agents'

const workflow = createFinancialWorkflowInstance()
const healthStatus = await validateAgentsHealth(workflow)

console.log('系统健康状态:', healthStatus)
```

## 错误处理

系统内置了完善的错误处理机制：

- 自动重试机制
- 错误恢复策略
- 详细的错误日志
- 优雅降级处理

## 扩展开发

### 添加新代理

1. 继承 `BaseAgent` 类
2. 实现 `processTask` 和 `updateState` 方法
3. 在工作流中注册新代理

```typescript
class CustomAgent extends BaseAgent {
  protected async processTask(state: WorkflowState, messages: BaseMessage[]) {
    // 实现具体逻辑
  }
  
  protected async updateState(state: WorkflowState, result: any) {
    // 更新状态
  }
}
```

### 添加新工具

在 `tools/index.ts` 中添加新的工具函数：

```typescript
export const customTool = async (input: any) => {
  // 工具逻辑
  return { success: true, data: result }
}
```

## 性能优化

- 使用连接池管理数据库连接
- 实现结果缓存机制
- 异步并行处理
- 智能重试策略

## 监控和日志

系统集成了完善的监控和日志功能：

- 执行时间监控
- 错误率统计
- 性能指标收集
- 详细的执行日志

## 版本信息

- 当前版本：2.0.0
- 基于：LangGraph StateGraph
- 更新日期：2024年
