import { Request, Response, NextFunction } from 'express'
import { logger } from '@/utils/logger'
import {
  AppError,
  ValidationError,
  AuthenticationError,
  AuthorizationError,
  NotFoundError,
  ConflictError,
} from '@/types'

export function errorHandler(
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void {
  logger.error('错误处理中间件捕获错误:', {
    message: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  })

  // 如果是已知的应用错误
  if (error instanceof AppError) {
    res.status(error.statusCode).json({
      success: false,
      error: {
        code: error.code,
        message: error.message,
        details: (error as any).details || undefined,
      },
      timestamp: new Date().toISOString(),
    })
    return
  }

  // 处理Zod验证错误
  if (error.name === 'ZodError') {
    const zodError = error as any
    res.status(400).json({
      success: false,
      error: {
        code: 'VALIDATION_ERROR',
        message: '请求参数验证失败',
        details: zodError.errors,
      },
      timestamp: new Date().toISOString(),
    })
    return
  }

  // 处理JWT错误
  if (error.name === 'JsonWebTokenError') {
    res.status(401).json({
      success: false,
      error: {
        code: 'INVALID_TOKEN',
        message: '无效的访问令牌',
      },
      timestamp: new Date().toISOString(),
    })
    return
  }

  if (error.name === 'TokenExpiredError') {
    res.status(401).json({
      success: false,
      error: {
        code: 'TOKEN_EXPIRED',
        message: '访问令牌已过期',
      },
      timestamp: new Date().toISOString(),
    })
    return
  }

  // 处理数据库错误
  if (error.message.includes('UNIQUE constraint failed')) {
    res.status(409).json({
      success: false,
      error: {
        code: 'DUPLICATE_RESOURCE',
        message: '资源已存在',
      },
      timestamp: new Date().toISOString(),
    })
    return
  }

  if (error.message.includes('FOREIGN KEY constraint failed')) {
    res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_REFERENCE',
        message: '引用的资源不存在',
      },
      timestamp: new Date().toISOString(),
    })
    return
  }

  // 处理语法错误
  if (error instanceof SyntaxError && 'body' in error) {
    res.status(400).json({
      success: false,
      error: {
        code: 'INVALID_JSON',
        message: '请求体JSON格式错误',
      },
      timestamp: new Date().toISOString(),
    })
    return
  }

  // 默认服务器错误
  res.status(500).json({
    success: false,
    error: {
      code: 'INTERNAL_ERROR',
      message: process.env.NODE_ENV === 'production' ? '服务器内部错误' : error.message,
    },
    timestamp: new Date().toISOString(),
  })
}

// 异步错误包装器
export function asyncHandler(fn: Function) {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next)
  }
}

// 创建错误的辅助函数
export function createError(
  message: string,
  statusCode: number = 500,
  code: string = 'INTERNAL_ERROR'
) {
  return new AppError(message, statusCode, code)
}

export function createValidationError(message: string, details?: any) {
  return new ValidationError(message, details)
}

export function createAuthenticationError(message?: string) {
  return new AuthenticationError(message)
}

export function createAuthorizationError(message?: string) {
  return new AuthorizationError(message)
}

export function createNotFoundError(message?: string) {
  return new NotFoundError(message)
}

export function createConflictError(message?: string) {
  return new ConflictError(message)
}
