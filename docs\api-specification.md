# AI 驱动资产管理应用 - API 接口规范

## API 设计原则

- **RESTful 设计**: 遵循 REST 架构风格
- **统一响应格式**: 所有 API 返回统一的 JSON 格式
- **版本控制**: 使用 URL 路径版本控制 `/api/v1/`
- **认证授权**: JWT Token 认证
- **错误处理**: 标准 HTTP 状态码和错误信息
- **分页支持**: 列表接口支持分页
- **数据验证**: 请求参数严格验证

## 通用响应格式

### 成功响应

```json
{
  "success": true,
  "data": {}, // 或 []
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 错误响应

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "参数验证失败",
    "details": {
      "field": "email",
      "reason": "邮箱格式不正确"
    }
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 分页响应

```json
{
  "success": true,
  "data": [],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 100,
    "totalPages": 5,
    "hasNext": true,
    "hasPrev": false
  },
  "message": "获取成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 1. 认证模块 API

### 用户注册

```
POST /api/v1/auth/register
Content-Type: application/json

Request Body:
{
  "email": "<EMAIL>",
  "password": "password123",
  "name": "用户姓名"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "用户姓名",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    "token": "jwt_token_here"
  },
  "message": "注册成功"
}
```

### 用户登录

```
POST /api/v1/auth/login
Content-Type: application/json

Request Body:
{
  "email": "<EMAIL>",
  "password": "password123"
}

Response:
{
  "success": true,
  "data": {
    "user": {
      "id": "user_123",
      "email": "<EMAIL>",
      "name": "用户姓名"
    },
    "token": "jwt_token_here",
    "expiresIn": 86400
  },
  "message": "登录成功"
}
```

### 刷新 Token

```
POST /api/v1/auth/refresh
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "token": "new_jwt_token_here",
    "expiresIn": 86400
  },
  "message": "Token刷新成功"
}
```

### 用户信息

```
GET /api/v1/auth/profile
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "id": "user_123",
    "email": "<EMAIL>",
    "name": "用户姓名",
    "avatarUrl": "https://example.com/avatar.jpg",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

## 2. 资产管理 API

### 获取资产列表

```
GET /api/v1/assets?page=1&limit=20&category=stocks&sortBy=currentValue&sortOrder=desc
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "asset_123",
      "name": "苹果股票",
      "description": "AAPL股票投资",
      "category": {
        "id": "stocks",
        "name": "股票",
        "icon": "trending-up",
        "color": "#2196F3"
      },
      "currentValue": 15000.00,
      "purchasePrice": 12000.00,
      "purchaseDate": "2023-01-01",
      "currency": "USD",
      "gainLoss": 3000.00,
      "gainLossPercentage": 25.00,
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 50,
    "totalPages": 3
  }
}
```

### 创建资产

```
POST /api/v1/assets
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "name": "苹果股票",
  "description": "AAPL股票投资",
  "categoryId": "stocks",
  "currentValue": 15000.00,
  "purchasePrice": 12000.00,
  "purchaseDate": "2023-01-01",
  "currency": "USD",
  "metadata": {
    "symbol": "AAPL",
    "shares": 100
  }
}

Response:
{
  "success": true,
  "data": {
    "id": "asset_123",
    "name": "苹果股票",
    "description": "AAPL股票投资",
    "categoryId": "stocks",
    "currentValue": 15000.00,
    "purchasePrice": 12000.00,
    "purchaseDate": "2023-01-01",
    "currency": "USD",
    "metadata": {
      "symbol": "AAPL",
      "shares": 100
    },
    "createdAt": "2024-01-01T00:00:00Z"
  },
  "message": "资产创建成功"
}
```

### 更新资产

```
PUT /api/v1/assets/:id
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "name": "苹果股票",
  "currentValue": 16000.00,
  "description": "更新后的描述"
}

Response:
{
  "success": true,
  "data": {
    "id": "asset_123",
    "name": "苹果股票",
    "currentValue": 16000.00,
    "updatedAt": "2024-01-01T00:00:00Z"
  },
  "message": "资产更新成功"
}
```

### 删除资产

```
DELETE /api/v1/assets/:id
Authorization: Bearer <token>

Response:
{
  "success": true,
  "message": "资产删除成功"
}
```

### 获取资产历史

```
GET /api/v1/assets/:id/history?startDate=2023-01-01&endDate=2024-01-01
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "history_123",
      "value": 15000.00,
      "recordDate": "2024-01-01",
      "source": "manual",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 资产统计

```
GET /api/v1/assets/statistics
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "totalValue": 100000.00,
    "totalGainLoss": 15000.00,
    "totalGainLossPercentage": 17.65,
    "categoryBreakdown": [
      {
        "categoryId": "stocks",
        "categoryName": "股票",
        "value": 60000.00,
        "percentage": 60.00,
        "count": 5
      }
    ],
    "currencyBreakdown": [
      {
        "currency": "USD",
        "value": 80000.00,
        "percentage": 80.00
      }
    ]
  }
}
```

## 3. 负债管理 API

### 获取负债列表

```
GET /api/v1/debts?page=1&limit=20&type=credit_card
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "debt_123",
      "name": "信用卡债务",
      "type": {
        "id": "credit_card",
        "name": "信用卡",
        "icon": "credit-card",
        "color": "#F44336"
      },
      "principalAmount": 10000.00,
      "currentBalance": 8500.00,
      "interestRate": 0.18,
      "monthlyPayment": 500.00,
      "startDate": "2023-01-01",
      "dueDate": "2025-01-01",
      "currency": "USD",
      "paymentProgress": 15.00,
      "remainingMonths": 17,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 创建负债

```
POST /api/v1/debts
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "name": "信用卡债务",
  "typeId": "credit_card",
  "principalAmount": 10000.00,
  "currentBalance": 10000.00,
  "interestRate": 0.18,
  "startDate": "2023-01-01",
  "monthlyPayment": 500.00,
  "currency": "USD"
}
```

### 记录还款

```
POST /api/v1/debts/:id/payments
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "amount": 500.00,
  "principalAmount": 450.00,
  "interestAmount": 50.00,
  "paymentDate": "2024-01-01"
}

Response:
{
  "success": true,
  "data": {
    "id": "payment_123",
    "debtId": "debt_123",
    "amount": 500.00,
    "principalAmount": 450.00,
    "interestAmount": 50.00,
    "paymentDate": "2024-01-01",
    "updatedBalance": 8050.00
  },
  "message": "还款记录成功"
}
```

### 负债统计

```
GET /api/v1/debts/statistics
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "totalDebt": 50000.00,
    "monthlyPayment": 2500.00,
    "totalInterestPaid": 5000.00,
    "averageInterestRate": 0.15,
    "debtToAssetRatio": 0.5,
    "typeBreakdown": [
      {
        "typeId": "credit_card",
        "typeName": "信用卡",
        "balance": 20000.00,
        "percentage": 40.00
      }
    ]
  }
}
```

## 4. AI 对话 API

### 创建对话

```
POST /api/v1/ai/conversations
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "title": "投资建议咨询",
  "contextType": "investment_advice",
  "contextData": {
    "assetIds": ["asset_123", "asset_456"],
    "timeframe": "1year"
  }
}

Response:
{
  "success": true,
  "data": {
    "id": "conv_123",
    "title": "投资建议咨询",
    "contextType": "investment_advice",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### 发送消息

```
POST /api/v1/ai/conversations/:id/messages
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "content": "请分析我的投资组合并给出建议",
  "modelId": "gpt-4"
}

Response:
{
  "success": true,
  "data": {
    "id": "msg_123",
    "conversationId": "conv_123",
    "role": "user",
    "content": "请分析我的投资组合并给出建议",
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

### 获取 AI 响应 (Server-Sent Events)

```
GET /api/v1/ai/conversations/:id/stream
Authorization: Bearer <token>
Accept: text/event-stream

Response (SSE):
data: {"type": "start", "messageId": "msg_124"}

data: {"type": "content", "content": "根据您的投资组合分析..."}

data: {"type": "function_call", "function": "get_asset_data", "args": {"assetId": "asset_123"}}

data: {"type": "end", "messageId": "msg_124", "tokensUsed": 150, "cost": 0.0045}
```

### 获取对话历史

```
GET /api/v1/ai/conversations/:id/messages?page=1&limit=50
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "msg_123",
      "role": "user",
      "content": "请分析我的投资组合",
      "createdAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": "msg_124",
      "role": "assistant",
      "content": "根据您的投资组合分析...",
      "modelId": "gpt-4",
      "tokensUsed": 150,
      "cost": 0.0045,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取 AI 模型列表

```
GET /api/v1/ai/models
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "gpt-4",
      "name": "GPT-4",
      "provider": "openai",
      "costPerToken": 0.00003,
      "capabilities": ["chat", "function_calling", "vision"],
      "isActive": true
    }
  ]
}
```

## 5. 新闻模块 API

### 获取新闻列表

```
GET /api/v1/news?page=1&limit=20&category=finance&startDate=2024-01-01
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "news_123",
      "title": "股市今日大涨",
      "summary": "主要股指均出现上涨...",
      "url": "https://example.com/news/123",
      "source": "财经新闻",
      "author": "记者姓名",
      "publishedDate": "2024-01-01",
      "categories": ["finance", "stocks"],
      "relevanceScore": 0.85,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取新闻详情

```
GET /api/v1/news/:id
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "id": "news_123",
    "title": "股市今日大涨",
    "content": "完整新闻内容...",
    "summary": "主要股指均出现上涨...",
    "url": "https://example.com/news/123",
    "source": "财经新闻",
    "author": "记者姓名",
    "publishedDate": "2024-01-01",
    "categories": ["finance", "stocks"],
    "relevanceScore": 0.85,
    "metadata": {
      "readTime": 5,
      "sentiment": "positive"
    }
  }
}
```

## 6. 市场数据 API

### 获取市场指标

```
GET /api/v1/market/indicators
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "sp500",
      "name": "S&P 500",
      "symbol": "SPX",
      "category": "index",
      "currentValue": 4500.00,
      "changeAmount": 50.00,
      "changePercentage": 1.12,
      "lastUpdated": "2024-01-01T16:00:00Z"
    }
  ]
}
```

### 获取历史市场数据

```
GET /api/v1/market/indicators/:id/history?startDate=2023-01-01&endDate=2024-01-01&interval=daily
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "date": "2024-01-01",
      "value": 4500.00,
      "changeAmount": 50.00,
      "changePercentage": 1.12
    }
  ]
}
```

## 7. 日历事件 API

### 获取日历事件

```
GET /api/v1/calendar/events?startDate=2024-01-01&endDate=2024-01-31&type=payment
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": [
    {
      "id": "event_123",
      "title": "信用卡还款",
      "description": "每月信用卡还款提醒",
      "eventType": "payment",
      "startTime": "2024-01-15T09:00:00Z",
      "endTime": "2024-01-15T09:30:00Z",
      "isRecurring": true,
      "recurrenceRule": {
        "frequency": "monthly",
        "interval": 1,
        "dayOfMonth": 15
      }
    }
  ]
}
```

### 创建日历事件

```
POST /api/v1/calendar/events
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "title": "投资组合回顾",
  "description": "季度投资组合回顾和调整",
  "eventType": "review",
  "startTime": "2024-04-01T14:00:00Z",
  "endTime": "2024-04-01T15:00:00Z",
  "isRecurring": true,
  "recurrenceRule": {
    "frequency": "quarterly",
    "interval": 1
  }
}
```

## 8. 用户设置 API

### 获取用户设置

```
GET /api/v1/settings
Authorization: Bearer <token>

Response:
{
  "success": true,
  "data": {
    "preferredCurrency": "USD",
    "timezone": "Asia/Shanghai",
    "notificationSettings": {
      "email": true,
      "push": false,
      "marketUpdates": true,
      "paymentReminders": true
    },
    "dashboardLayout": {
      "widgets": ["assets", "debts", "market", "news"],
      "chartPreferences": {
        "defaultTimeframe": "1year",
        "chartType": "line"
      }
    },
    "aiSettings": {
      "defaultModel": "gpt-4",
      "maxTokens": 2000,
      "temperature": 0.7
    }
  }
}
```

### 更新用户设置

```
PUT /api/v1/settings
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "preferredCurrency": "CNY",
  "timezone": "Asia/Shanghai",
  "notificationSettings": {
    "email": true,
    "marketUpdates": false
  }
}
```

### 管理 API Keys

```
POST /api/v1/settings/api-keys
Authorization: Bearer <token>
Content-Type: application/json

Request Body:
{
  "provider": "openai",
  "apiKey": "sk-...",
  "name": "我的OpenAI Key"
}

Response:
{
  "success": true,
  "data": {
    "id": "key_123",
    "provider": "openai",
    "name": "我的OpenAI Key",
    "maskedKey": "sk-...****",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```

## 错误代码定义

| 错误代码            | HTTP 状态码 | 描述             |
| ------------------- | ----------- | ---------------- |
| VALIDATION_ERROR    | 400         | 请求参数验证失败 |
| UNAUTHORIZED        | 401         | 未授权访问       |
| FORBIDDEN           | 403         | 权限不足         |
| NOT_FOUND           | 404         | 资源不存在       |
| CONFLICT            | 409         | 资源冲突         |
| RATE_LIMIT_EXCEEDED | 429         | 请求频率超限     |
| INTERNAL_ERROR      | 500         | 服务器内部错误   |
| AI_SERVICE_ERROR    | 502         | AI 服务错误      |
| DATABASE_ERROR      | 503         | 数据库错误       |

## API 安全措施

### 1. 认证机制

- JWT Token 认证
- Token 过期时间控制
- 刷新 Token 机制

### 2. 权限控制

- 基于用户的资源访问控制
- API Key 权限隔离
- 管理员权限分离

### 3. 请求限制

- 基于 IP 的请求频率限制
- 基于用户的请求频率限制
- AI API 调用频率控制

### 4. 数据验证

- 输入参数严格验证
- SQL 注入防护
- XSS 攻击防护

### 5. 日志监控

- API 访问日志
- 错误日志记录
- 性能监控指标

## API 文档生成

使用 Swagger/OpenAPI 3.0 规范生成交互式 API 文档，包含：

- 完整的接口定义
- 请求/响应示例
- 参数说明
- 错误代码说明
- 在线测试功能
