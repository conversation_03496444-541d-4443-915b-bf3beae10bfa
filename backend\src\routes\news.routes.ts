import { Router } from 'express'
import { newsController } from '../controllers/newsController'
import { authMiddleware } from '../middleware/authMiddleware'
import { asyncHandler } from '../middleware/errorHandler'

const router: Router = Router()

// 应用认证中间件（单用户模式）
router.use(authMiddleware)

// 获取新闻分类（无需认证）
router.get('/categories', asyncHandler(newsController.getNewsCategories))

// 获取新闻列表
router.get('/', asyncHandler(newsController.getNews))

// 获取单条新闻详情
router.get('/:newsId', asyncHandler(newsController.getNewsById))

export default router
