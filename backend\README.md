# AI 资产管理应用 - 后端服务

这是 AI 驱动的资产管理和理财投资建议应用的后端服务，使用 Node.js 22 + TypeScript + Express.js 构建。

## 技术栈

- **运行时**: Node.js 22+
- **语言**: TypeScript
- **Web 框架**: Express.js
- **数据库**: SQLite3 + Drizzle ORM
- **AI 集成**: Vercel AI SDK
- **认证**: JWT
- **包管理**: pnpm

## 快速开始

### 1. 环境要求

- Node.js 22+
- pnpm 8+

### 2. 安装依赖

```bash
cd backend
pnpm install
```

### 3. 环境配置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
# 配置JWT密钥、数据库路径、AI API Keys等
```

### 4. 数据库初始化

```bash
# 运行数据库迁移
pnpm run db:migrate

# 插入种子数据
pnpm run db:seed
```

### 5. 启动开发服务器

```bash
# 开发模式
pnpm run dev

# 生产模式
pnpm run build
pnpm run start
```

## 项目结构

```
backend/
├── src/
│   ├── controllers/     # 控制器层
│   ├── services/        # 业务服务层
│   ├── routes/          # 路由定义
│   ├── middleware/      # 中间件
│   ├── database/        # 数据库相关
│   ├── ai/             # AI服务
│   ├── types/          # 类型定义
│   ├── utils/          # 工具函数
│   └── index.ts        # 应用入口
├── dist/               # 编译输出
├── package.json
├── tsconfig.json
└── .env.example
```

## API 端点

### 健康检查

- `GET /api/v1/health` - 基础健康检查
- `GET /api/v1/health/detailed` - 详细健康检查

### 认证模块

- `POST /api/v1/auth/register` - 用户注册
- `POST /api/v1/auth/login` - 用户登录
- `GET /api/v1/auth/profile` - 获取用户信息

### 资产管理

- `GET /api/v1/assets` - 获取资产列表
- `POST /api/v1/assets` - 创建资产
- `PUT /api/v1/assets/:id` - 更新资产
- `DELETE /api/v1/assets/:id` - 删除资产
- `GET /api/v1/assets/statistics` - 资产统计

### 负债管理

- `GET /api/v1/debts` - 获取负债列表
- `POST /api/v1/debts` - 创建负债
- `POST /api/v1/debts/:id/payments` - 记录还款

### AI 对话

- `POST /api/v1/ai/conversations` - 创建对话
- `POST /api/v1/ai/conversations/:id/messages` - 发送消息
- `GET /api/v1/ai/models` - 获取可用模型

### 数据更新（用户触发）

- `POST /api/v1/data/update-market` - 更新市场数据
- `POST /api/v1/data/update-news` - 更新新闻数据
- `GET /api/v1/data/update-status/:taskId` - 获取更新状态

### 数据导出

- `GET /api/v1/export/database` - 导出 SQLite 文件
- `GET /api/v1/export/data?format=json` - 导出 JSON 数据

## 环境变量说明

```env
# 应用配置
NODE_ENV=development
PORT=3001
FRONTEND_URL=http://localhost:3000

# 数据库配置
DATABASE_PATH=../data/database.sqlite

# 认证配置
JWT_SECRET=your-jwt-secret-key
JWT_EXPIRES_IN=24h
ENCRYPTION_KEY=your-encryption-key

# AI服务配置
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key

# 外部API配置
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
NEWS_API_KEY=your-news-api-key

# 应用设置
LOG_LEVEL=info
CORS_ORIGIN=http://localhost:3000
```

## 开发指南

### 添加新的 API 端点

1. 在 `src/routes/` 中创建或修改路由文件
2. 在 `src/controllers/` 中添加控制器逻辑
3. 在 `src/services/` 中添加业务逻辑
4. 更新类型定义 `src/types/index.ts`

### 数据库操作

```bash
# 生成数据库迁移
pnpm run db:generate

# 推送schema到数据库
pnpm run db:push

# 运行自定义迁移
pnpm run db:migrate
```

### 代码规范

```bash
# 代码检查
pnpm run lint

# 自动修复
pnpm run lint:fix

# 运行测试
pnpm run test
```

## 部署

### Docker 部署

```bash
# 构建镜像
docker build -t ai-asset-manager-backend .

# 运行容器
docker run -p 3001:3001 -v ./data:/app/data ai-asset-manager-backend
```

### 生产部署

```bash
# 构建项目
pnpm run build

# 启动生产服务器
pnpm run start:prod
```

## 故障排除

### 常见问题

1. **端口被占用**

   ```bash
   # 检查端口占用
   netstat -ano | findstr :3001

   # 修改端口
   # 在.env文件中设置 PORT=3002
   ```

2. **数据库连接失败**

   ```bash
   # 检查数据库文件路径
   # 确保data目录存在
   mkdir -p ../data
   ```

3. **TypeScript 编译错误**
   ```bash
   # 清理并重新构建
   rm -rf dist
   pnpm run build
   ```

## 许可证

MIT License
