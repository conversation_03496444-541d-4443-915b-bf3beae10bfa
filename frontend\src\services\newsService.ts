import { apiClient } from './api'
import type { ApiResponse } from '../types'

export interface News {
  id: string
  title: string
  content?: string
  summary?: string
  url?: string
  source: string
  publishedDate: string
  relevanceScore?: number
  createdAt: Date
}

export interface NewsCategory {
  id: string
  name: string
  description: string
}

export interface GetNewsParams {
  page?: number
  limit?: number
  category?: string
}

class NewsService {
  /**
   * 获取新闻列表
   */
  async getNews(params?: GetNewsParams): Promise<
    ApiResponse<{
      data: News[]
      pagination: {
        page: number
        limit: number
        total: number
        totalPages: number
        hasNext: boolean
        hasPrev: boolean
      }
    }>
  > {
    return await apiClient.get('/news', params)
  }

  /**
   * 获取单条新闻详情
   */
  async getNewsById(newsId: string): Promise<ApiResponse<News>> {
    return await apiClient.get<News>(`/news/${newsId}`)
  }

  /**
   * 获取新闻分类
   */
  async getNewsCategories(): Promise<ApiResponse<NewsCategory[]>> {
    return await apiClient.get<NewsCategory[]>('/news/categories')
  }

  /**
   * 获取热门新闻
   */
  async getHotNews(limit: number = 10): Promise<ApiResponse<News[]>> {
    return await this.getNews({ limit }).then((response) => ({
      ...response,
      data: response.data?.data || [],
    }))
  }

  /**
   * 根据分类获取新闻
   */
  async getNewsByCategory(
    category: string,
    limit: number = 20
  ): Promise<ApiResponse<News[]>> {
    return await this.getNews({ category, limit }).then((response) => ({
      ...response,
      data: response.data?.data || [],
    }))
  }

  /**
   * 搜索新闻
   */
  async searchNews(query: string, limit: number = 20): Promise<ApiResponse<News[]>> {
    // 这里可以实现新闻搜索功能
    // 目前返回所有新闻的过滤结果
    const response = await this.getNews({ limit })
    if (response.success && response.data?.data) {
      const filteredNews = response.data.data.filter(
        (news) =>
          news.title.toLowerCase().includes(query.toLowerCase()) ||
          news.summary?.toLowerCase().includes(query.toLowerCase())
      )
      return {
        ...response,
        data: filteredNews,
      }
    }
    return response as any
  }

  /**
   * 获取相关新闻推荐
   */
  async getRelatedNews(
    newsId: string,
    limit: number = 5
  ): Promise<ApiResponse<News[]>> {
    // 这里可以实现基于新闻内容的推荐算法
    // 目前返回最新的几条新闻作为相关推荐
    return await this.getNews({ limit }).then((response) => ({
      ...response,
      data: response.data?.data?.filter((news) => news.id !== newsId) || [],
    }))
  }

  /**
   * 标记新闻为已读
   */
  async markAsRead(newsId: string): Promise<void> {
    // 这里可以实现新闻阅读状态的记录
    // 目前只是一个占位符方法
    console.log(`标记新闻 ${newsId} 为已读`)
  }

  /**
   * 收藏新闻
   */
  async bookmarkNews(newsId: string): Promise<void> {
    // 这里可以实现新闻收藏功能
    // 目前只是一个占位符方法
    console.log(`收藏新闻 ${newsId}`)
  }

  /**
   * 取消收藏新闻
   */
  async unbookmarkNews(newsId: string): Promise<void> {
    // 这里可以实现取消收藏功能
    // 目前只是一个占位符方法
    console.log(`取消收藏新闻 ${newsId}`)
  }

  /**
   * 获取收藏的新闻
   */
  async getBookmarkedNews(): Promise<ApiResponse<News[]>> {
    // 这里可以实现获取收藏新闻的功能
    // 目前返回空数组
    return {
      success: true,
      data: [],
      message: '获取收藏新闻成功',
      timestamp: new Date().toISOString(),
    }
  }
}

export const newsService = new NewsService()
