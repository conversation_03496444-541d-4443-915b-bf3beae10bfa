import React from 'react'
import { Layout, Button, Dropdown, Avatar, Space, Typography } from 'antd'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  BellOutlined,
} from '@ant-design/icons'
import type { MenuProps } from 'antd'
import { useAppStore } from '../../stores/appStore'
import { useUserStore } from '../../stores/userStore'

const { Header: AntHeader } = Layout
const { Text } = Typography

export const Header: React.FC = () => {
  const { sidebarCollapsed, toggleSidebar } = useAppStore()
  const { user, logout } = useUserStore()

  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: logout,
    },
  ]

  return (
    <AntHeader className="bg-white shadow-sm px-6 flex items-center justify-between">
      <div className="flex items-center space-x-4">
        <Button
          type="text"
          icon={sidebarCollapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
          onClick={toggleSidebar}
          className="text-gray-600 hover:text-gray-900"
        />
        <div className="text-lg font-semibold text-gray-800">AI 资产管理系统</div>
      </div>

      <div className="flex items-center space-x-4">
        <Button
          type="text"
          icon={<BellOutlined />}
          className="text-gray-600 hover:text-gray-900"
        />

        <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
          <Space className="cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg transition-colors">
            <Avatar size="small" icon={<UserOutlined />} className="bg-blue-500" />
            <Text className="text-gray-700">{user?.name || '用户'}</Text>
          </Space>
        </Dropdown>
      </div>
    </AntHeader>
  )
}
