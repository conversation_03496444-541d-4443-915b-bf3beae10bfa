import type { BaseMessage } from '@langchain/core/messages'
import type { StateGraphArgs } from '@langchain/langgraph'

// StateGraph工作流状态接口 - 这是StateGraph的核心状态
export interface WorkflowState {
  messages: BaseMessage[]
  userQuery: string
  userId: string
  conversationId: string
  modelId: string

  // 用户数据
  userAssets?: any[]
  userDebts?: any[]
  userProfile?: any

  // 分析结果
  financialAnalysis?: FinancialAnalysisResult | undefined
  investmentAdvice?: InvestmentAdviceResult | undefined
  riskAssessment?: RiskAssessmentResult | undefined
  newsAnalysis?: NewsAnalysisResult | undefined

  // 工作流控制
  currentStep: string
  nextSteps: string[]
  isComplete: boolean
  error?: string | undefined

  // 上下文和记忆
  context: Record<string, any>
  memory: WorkflowMemory

  // StateGraph特定字段
  nodeResults?: Record<string, any>
  executionPath?: string[]
  retryCount?: number
  lastNodeExecuted?: string
  conditionalFlags?: Record<string, boolean>
}

// StateGraph节点函数类型
export type StateGraphNode = (state: WorkflowState) => Promise<Partial<WorkflowState>>

// StateGraph条件函数类型
export type StateGraphCondition = (state: WorkflowState) => string | string[]

// StateGraph配置
export interface StateGraphConfig {
  name: string
  description: string
  nodes: Record<string, StateGraphNode>
  edges: StateGraphEdge[]
  conditionalEdges: StateGraphConditionalEdge[]
  entryPoint: string
  finishPoint: string
  maxRetries: number
  timeout: number
}

// StateGraph边定义
export interface StateGraphEdge {
  from: string
  to: string
  condition?: string
}

// StateGraph条件边定义
export interface StateGraphConditionalEdge {
  from: string
  condition: StateGraphCondition
  mapping: Record<string, string>
}

// StateGraph执行上下文
export interface StateGraphExecutionContext {
  graphId: string
  executionId: string
  startTime: Date
  currentNode?: string
  visitedNodes: string[]
  nodeExecutionTimes: Record<string, number>
  errors: StateGraphError[]
  checkpoints: StateGraphCheckpoint[]
}

// StateGraph错误
export interface StateGraphError {
  nodeId: string
  error: string
  timestamp: Date
  retryAttempt: number
  recoverable: boolean
}

// StateGraph检查点
export interface StateGraphCheckpoint {
  nodeId: string
  state: Partial<WorkflowState>
  timestamp: Date
  checkpointId: string
}

// 财务分析结果
export interface FinancialAnalysisResult {
  totalAssets: number
  totalDebts: number
  netWorth: number
  assetAllocation: AssetAllocation[]
  financialHealthScore: number
  cashFlow: CashFlowAnalysis
  recommendations: string[]
  timestamp: Date
}

// 资产配置
export interface AssetAllocation {
  category: string
  value: number
  percentage: number
  risk: 'low' | 'medium' | 'high'
}

// 现金流分析
export interface CashFlowAnalysis {
  monthlyIncome: number
  monthlyExpenses: number
  netCashFlow: number
  emergencyFundRatio: number
}

// 投资建议结果
export interface InvestmentAdviceResult {
  riskProfile: 'conservative' | 'moderate' | 'aggressive'
  recommendedAllocation: AssetAllocation[]
  specificRecommendations: InvestmentRecommendation[]
  timeHorizon: string
  expectedReturn: number
  reasoning: string
  timestamp: Date
}

// 投资建议
export interface InvestmentRecommendation {
  action: 'buy' | 'sell' | 'hold' | 'rebalance'
  asset: string
  amount?: number
  percentage?: number
  reason: string
  priority: 'high' | 'medium' | 'low'
}

// 风险评估结果
export interface RiskAssessmentResult {
  overallRisk: 'low' | 'medium' | 'high'
  riskFactors: RiskFactor[]
  diversificationScore: number
  volatilityAnalysis: VolatilityAnalysis
  recommendations: string[]
  timestamp: Date
}

// 风险因素
export interface RiskFactor {
  type: string
  level: 'low' | 'medium' | 'high'
  description: string
  impact: number
  mitigation: string
}

// 波动性分析
export interface VolatilityAnalysis {
  portfolioVolatility: number
  sharpeRatio: number
  maxDrawdown: number
  valueAtRisk: number
}

// 新闻分析结果
export interface NewsAnalysisResult {
  relevantNews: NewsItem[]
  marketSentiment: 'positive' | 'neutral' | 'negative'
  impactAnalysis: NewsImpact[]
  actionableInsights: string[]
  timestamp: Date
}

// 新闻项目
export interface NewsItem {
  title: string
  summary: string
  source: string
  publishedAt: Date
  relevanceScore: number
  sentimentScore: number
  categories: string[]
}

// 新闻影响
export interface NewsImpact {
  asset: string
  impact: 'positive' | 'negative' | 'neutral'
  confidence: number
  reasoning: string
}

// 工作流记忆
export interface WorkflowMemory {
  conversationHistory: ConversationMemory[]
  userPreferences: UserPreferences
  previousAnalyses: PreviousAnalysis[]
  learnings: Learning[]
}

// 对话记忆
export interface ConversationMemory {
  timestamp: Date
  userInput: string
  agentResponse: string
  context: Record<string, any>
}

// 用户偏好
export interface UserPreferences {
  riskTolerance: 'low' | 'medium' | 'high' | 'moderate'
  investmentGoals: string[]
  timeHorizon: string
  preferredAssetTypes: string[]
  excludedAssetTypes: string[]
}

// 历史分析
export interface PreviousAnalysis {
  type: 'financial' | 'investment' | 'risk' | 'news'
  timestamp: Date
  result: any
  accuracy?: number
}

// 学习记录
export interface Learning {
  pattern: string
  outcome: string
  confidence: number
  timestamp: Date
}

// 代理配置
export interface AgentConfig {
  name: string
  description: string
  systemPrompt: string
  tools: string[]
  maxIterations: number
  temperature: number
  modelId: string
}

// 工作流配置
export interface WorkflowConfig {
  name: string
  description: string
  agents: AgentConfig[]
  flow: WorkflowNode[]
  maxExecutionTime: number
  enableMemory: boolean
  enableTools: boolean
}

// 工作流节点
export interface WorkflowNode {
  id: string
  type: 'agent' | 'condition' | 'parallel' | 'sequential'
  agentName?: string
  condition?: string
  children?: string[]
  next?: string
}

// 工作流执行结果
export interface WorkflowExecutionResult {
  success: boolean
  state: WorkflowState
  executionTime: number
  steps: ExecutionStep[]
  error?: string | undefined
  executionContext?: StateGraphExecutionContext
}

// 执行步骤
export interface ExecutionStep {
  stepId: string
  agentName: string
  startTime: Date
  endTime: Date
  input: any
  output: any
  success: boolean
  error?: string
}

// 工具调用结果
export interface ToolCallResult {
  toolName: string
  input: any
  output: any
  success: boolean
  error?: string
  executionTime: number
}

// StateGraph节点状态
export interface NodeExecutionState {
  nodeId: string
  status: 'pending' | 'running' | 'completed' | 'failed' | 'skipped'
  startTime?: Date
  endTime?: Date
  input?: Partial<WorkflowState>
  output?: Partial<WorkflowState>
  error?: string
  retryCount: number
}

// StateGraph工作流实例
export interface StateGraphWorkflowInstance {
  instanceId: string
  graphConfig: StateGraphConfig
  currentState: WorkflowState
  executionContext: StateGraphExecutionContext
  nodeStates: Record<string, NodeExecutionState>
  isRunning: boolean
  isPaused: boolean
  isCompleted: boolean
  isFailed: boolean
}
