import { apiClient } from './api'
import type { ApiResponse } from '../types'

export interface UserSettings {
  id: string
  userId: string
  aiApiKeys: Record<string, { configured: boolean; masked: string }>
  preferredCurrency: string
  timezone: string
  dashboardLayout: any
  updatedAt: Date
}

export interface UpdateSettingsRequest {
  aiApiKeys?: Record<string, string>
  preferredCurrency?: string
  timezone?: string
  dashboardLayout?: any
}

export interface Currency {
  code: string
  name: string
  symbol: string
}

export interface Timezone {
  value: string
  label: string
  offset: string
}

class SettingsService {
  /**
   * 获取用户设置
   */
  async getUserSettings(): Promise<ApiResponse<UserSettings>> {
    return await apiClient.get<UserSettings>('/settings')
  }

  /**
   * 更新用户设置
   */
  async updateUserSettings(
    data: UpdateSettingsRequest
  ): Promise<ApiResponse<UserSettings>> {
    return await apiClient.put<UserSettings>('/settings', data)
  }

  /**
   * 重置用户设置
   */
  async resetUserSettings(): Promise<ApiResponse<UserSettings>> {
    return await apiClient.post<UserSettings>('/settings/reset')
  }

  /**
   * 获取支持的货币列表
   */
  async getSupportedCurrencies(): Promise<ApiResponse<Currency[]>> {
    return await apiClient.get<Currency[]>('/settings/currencies')
  }

  /**
   * 获取支持的时区列表
   */
  async getSupportedTimezones(): Promise<ApiResponse<Timezone[]>> {
    return await apiClient.get<Timezone[]>('/settings/timezones')
  }

  /**
   * 更新AI API Keys
   */
  async updateAIApiKeys(
    apiKeys: Record<string, string>
  ): Promise<ApiResponse<UserSettings>> {
    return await this.updateUserSettings({ aiApiKeys: apiKeys })
  }

  /**
   * 更新首选货币
   */
  async updatePreferredCurrency(currency: string): Promise<ApiResponse<UserSettings>> {
    return await this.updateUserSettings({ preferredCurrency: currency })
  }

  /**
   * 更新时区
   */
  async updateTimezone(timezone: string): Promise<ApiResponse<UserSettings>> {
    return await this.updateUserSettings({ timezone })
  }

  /**
   * 更新仪表板布局
   */
  async updateDashboardLayout(layout: any): Promise<ApiResponse<UserSettings>> {
    return await this.updateUserSettings({ dashboardLayout: layout })
  }

  /**
   * 验证API Key是否有效
   */
  async validateApiKey(
    provider: string,
    apiKey: string
  ): Promise<ApiResponse<{ valid: boolean; message?: string }>> {
    // 这里可以实现API Key验证逻辑
    // 目前返回模拟结果
    return {
      success: true,
      data: {
        valid: apiKey.length > 10, // 简单验证
        message: apiKey.length > 10 ? 'API Key有效' : 'API Key格式不正确',
      },
      message: 'API Key验证完成',
      timestamp: new Date().toISOString(),
    }
  }

  /**
   * 获取默认设置
   */
  async getDefaultSettings(): Promise<ApiResponse<Partial<UserSettings>>> {
    return {
      success: true,
      data: {
        aiApiKeys: {},
        preferredCurrency: 'USD',
        timezone: 'UTC',
        dashboardLayout: {
          widgets: [
            { id: 'assets', position: { x: 0, y: 0, w: 6, h: 4 } },
            { id: 'market', position: { x: 6, y: 0, w: 6, h: 4 } },
            { id: 'news', position: { x: 0, y: 4, w: 12, h: 4 } },
          ],
        },
      },
      message: '获取默认设置成功',
      timestamp: new Date().toISOString(),
    }
  }

  /**
   * 导出设置
   */
  async exportSettings(): Promise<void> {
    const settings = await this.getUserSettings()
    if (settings.success && settings.data) {
      const dataStr = JSON.stringify(settings.data, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })

      const link = document.createElement('a')
      link.href = URL.createObjectURL(dataBlob)
      link.download = `settings-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(link.href)
    }
  }

  /**
   * 导入设置
   */
  async importSettings(file: File): Promise<ApiResponse<UserSettings>> {
    try {
      const text = await file.text()
      const settings = JSON.parse(text)

      // 验证设置格式
      if (!this.validateSettingsFormat(settings)) {
        return {
          success: false,
          message: '设置文件格式不正确',
          timestamp: new Date().toISOString(),
        }
      }

      // 更新设置
      return await this.updateUserSettings({
        aiApiKeys: settings.aiApiKeys,
        preferredCurrency: settings.preferredCurrency,
        timezone: settings.timezone,
        dashboardLayout: settings.dashboardLayout,
      })
    } catch (error) {
      return {
        success: false,
        message:
          '导入设置失败：' + (error instanceof Error ? error.message : '未知错误'),
        timestamp: new Date().toISOString(),
      }
    }
  }

  /**
   * 验证设置格式
   */
  private validateSettingsFormat(settings: any): boolean {
    return (
      typeof settings === 'object' &&
      settings !== null &&
      (settings.preferredCurrency === undefined ||
        typeof settings.preferredCurrency === 'string') &&
      (settings.timezone === undefined || typeof settings.timezone === 'string') &&
      (settings.aiApiKeys === undefined || typeof settings.aiApiKeys === 'object') &&
      (settings.dashboardLayout === undefined ||
        typeof settings.dashboardLayout === 'object')
    )
  }

  /**
   * 获取当前用户的本地化设置
   */
  async getLocalizationSettings(): Promise<{
    currency: Currency
    timezone: Timezone
    dateFormat: string
    numberFormat: string
  }> {
    const [settingsResponse, currenciesResponse, timezonesResponse] = await Promise.all(
      [
        this.getUserSettings(),
        this.getSupportedCurrencies(),
        this.getSupportedTimezones(),
      ]
    )

    const settings = settingsResponse.data
    const currencies = currenciesResponse.data || []
    const timezones = timezonesResponse.data || []

    const currency =
      currencies.find((c) => c.code === settings?.preferredCurrency) || currencies[0]
    const timezone =
      timezones.find((t) => t.value === settings?.timezone) || timezones[0]

    return {
      currency: currency || { code: 'USD', name: '美元', symbol: '$' },
      timezone: timezone || { value: 'UTC', label: 'UTC', offset: '+00:00' },
      dateFormat: 'YYYY-MM-DD',
      numberFormat: '1,234.56',
    }
  }

  /**
   * 格式化货币显示
   */
  formatCurrency(amount: number, currencyCode?: string): string {
    const currency = currencyCode || 'USD'
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  /**
   * 格式化日期显示
   */
  formatDate(date: Date | string, timezone?: string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return new Intl.DateTimeFormat('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      timeZone: timezone || 'UTC',
    }).format(dateObj)
  }
}

export const settingsService = new SettingsService()
