import { apiClient } from './api'
import type { ApiResponse } from '../types'

export interface AIModel {
  id: string
  name: string
  provider: string
  modelId: string
  costPerToken?: number
  isActive: boolean
  createdAt: Date
}

export interface AIConversation {
  id: string
  userId: string
  title: string
  contextType?: string
  contextData?: any
  createdAt: Date
  updatedAt: Date
}

export interface AIMessage {
  id: string
  conversationId: string
  modelId: string
  role: 'user' | 'assistant' | 'system'
  content: string
  metadata?: any
  tokensUsed?: number
  cost?: number
  createdAt: Date
}

export interface CreateConversationRequest {
  title: string
  contextType?: string
  contextData?: any
}

export interface SendMessageRequest {
  content: string
  modelId: string
}

export interface AIUsageStats {
  totalConversations: number
  totalMessages: number
  totalTokensUsed: number
  totalCost: number
}

// 工作流相关类型定义
export interface WorkflowExecution {
  id: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  startTime: Date
  endTime?: Date
  totalExecutionTime?: number
  results: AgentResult[]
  error?: string
}

export interface WorkflowState {
  currentStep: string
  completedSteps: string[]
  isComplete: boolean
  data: any
}

export interface AgentResult {
  agentType: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  timestamp: Date
  executionTime?: number
  result?: {
    analysis: string
    recommendations?: string[]
    metrics?: Record<string, any>
  }
  error?: string
}

export interface ExecuteWorkflowRequest {
  assets: any[]
  modelId: string
  workflowType?: string
}

export interface ExecuteAgentRequest {
  agentType: string
  assets: any[]
  modelId: string
}

class AIService {
  /**
   * 获取可用的AI模型
   */
  async getAvailableModels(): Promise<ApiResponse<AIModel[]>> {
    return await apiClient.get<AIModel[]>('/ai/models')
  }

  /**
   * 获取用户对话列表
   */
  async getUserConversations(): Promise<ApiResponse<AIConversation[]>> {
    return await apiClient.get<AIConversation[]>('/ai/conversations')
  }

  /**
   * 创建新对话
   */
  async createConversation(
    data: CreateConversationRequest
  ): Promise<ApiResponse<AIConversation>> {
    return await apiClient.post<AIConversation>('/ai/conversations', data)
  }

  /**
   * 获取对话消息
   */
  async getConversationMessages(
    conversationId: string
  ): Promise<ApiResponse<AIMessage[]>> {
    return await apiClient.get<AIMessage[]>(
      `/ai/conversations/${conversationId}/messages`
    )
  }

  /**
   * 发送消息并获取AI回复
   */
  async sendMessage(
    conversationId: string,
    data: SendMessageRequest
  ): Promise<ApiResponse<{ userMessage: AIMessage; aiMessage: AIMessage }>> {
    return await apiClient.post<{ userMessage: AIMessage; aiMessage: AIMessage }>(
      `/ai/conversations/${conversationId}/messages`,
      data
    )
  }

  /**
   * 流式发送消息
   */
  async sendMessageStream(
    conversationId: string,
    data: SendMessageRequest,
    onChunk: (chunk: string) => void,
    onComplete: (message: AIMessage) => void,
    onError: (error: string) => void
  ): Promise<void> {
    try {
      const response = await fetch(
        `${
          import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api/v1'
        }/ai/conversations/${conversationId}/messages/stream`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(data),
        }
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const reader = response.body?.getReader()
      if (!reader) {
        throw new Error('无法获取响应流')
      }

      const decoder = new TextDecoder()
      let buffer = ''

      while (true) {
        const { done, value } = await reader.read()

        if (done) break

        buffer += decoder.decode(value, { stream: true })
        const lines = buffer.split('\n')
        buffer = lines.pop() || ''

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              switch (data.type) {
                case 'userMessage':
                  // 用户消息已发送
                  break
                case 'chunk':
                  onChunk(data.data)
                  break
                case 'complete':
                  onComplete(data.data)
                  return
                case 'error':
                  onError(data.message)
                  return
              }
            } catch (error) {
              console.warn('解析SSE数据失败:', error)
            }
          }
        }
      }
    } catch (error) {
      onError(error instanceof Error ? error.message : '发送消息失败')
    }
  }

  /**
   * 更新对话标题
   */
  async updateConversationTitle(
    conversationId: string,
    title: string
  ): Promise<ApiResponse<void>> {
    return await apiClient.put<void>(`/ai/conversations/${conversationId}/title`, {
      title,
    })
  }

  /**
   * 删除对话
   */
  async deleteConversation(conversationId: string): Promise<ApiResponse<void>> {
    return await apiClient.delete<void>(`/ai/conversations/${conversationId}`)
  }

  /**
   * 获取AI使用统计
   */
  async getAIUsageStats(): Promise<ApiResponse<AIUsageStats>> {
    return await apiClient.get<AIUsageStats>('/ai/usage/stats')
  }

  /**
   * 根据资产数据生成投资建议
   */
  async generateInvestmentAdvice(
    assets: any[],
    modelId: string = 'deepseek-chat'
  ): Promise<ApiResponse<{ userMessage: AIMessage; aiMessage: AIMessage }>> {
    // 创建投资建议对话
    const conversation = await this.createConversation({
      title: '投资建议分析',
      contextType: 'investment_advice',
      contextData: { assets },
    })

    if (!conversation.success || !conversation.data) {
      throw new Error('创建对话失败')
    }

    // 构建分析提示
    const prompt = `
请基于以下资产组合数据，为我提供专业的投资建议：

资产详情：
${assets
  .map(
    (asset) => `
- ${asset.name}: ${asset.currentValue} ${asset.currency}
  分类: ${asset.categoryId}
  ${asset.purchasePrice ? `购买价格: ${asset.purchasePrice}` : ''}
  ${asset.purchaseDate ? `购买日期: ${asset.purchaseDate}` : ''}
`
  )
  .join('')}

请从以下几个方面进行分析：
1. 当前资产配置分析
2. 风险评估
3. 优化建议
4. 具体操作建议

请提供专业、实用的建议。
    `

    return await this.sendMessage(conversation.data.id, {
      content: prompt.trim(),
      modelId,
    })
  }

  /**
   * 分析市场趋势
   */
  async analyzeMarketTrend(
    marketData: any[],
    modelId: string = 'deepseek-chat'
  ): Promise<ApiResponse<{ userMessage: AIMessage; aiMessage: AIMessage }>> {
    const conversation = await this.createConversation({
      title: '市场趋势分析',
      contextType: 'market_analysis',
      contextData: { marketData },
    })

    if (!conversation.success || !conversation.data) {
      throw new Error('创建对话失败')
    }

    const prompt = `
请基于以下市场数据，分析当前市场趋势：

市场数据：
${marketData
  .map(
    (data) => `
- ${data.name} (${data.symbol}): ${data.value}
  变化: ${data.change} (${data.changePercent}%)
  趋势: ${data.trend}
`
  )
  .join('')}

请提供：
1. 市场整体趋势分析
2. 主要影响因素
3. 短期和长期展望
4. 投资策略建议
    `

    return await this.sendMessage(conversation.data.id, {
      content: prompt.trim(),
      modelId,
    })
  }

  /**
   * 获取工作流状态
   */
  async getWorkflowStatus(): Promise<
    ApiResponse<{
      isHealthy: boolean
      agentInfo: Array<{ name: string; description: string }>
      config: {
        name: string
        description: string
        version: string
        agents: string[]
        maxExecutionTime: number
        enableMemory: boolean
        enableTools: boolean
      }
    }>
  > {
    return await apiClient.get<{
      isHealthy: boolean
      agentInfo: Array<{ name: string; description: string }>
      config: {
        name: string
        description: string
        version: string
        agents: string[]
        maxExecutionTime: number
        enableMemory: boolean
        enableTools: boolean
      }
    }>('/ai/workflow/status')
  }

  /**
   * 获取工作流执行历史
   */
  async getWorkflowHistory(workflowType: string = 'financial_analysis'): Promise<
    ApiResponse<{
      workflowType: string
      history: Array<{
        stepId: string
        agentName: string
        startTime: string
        endTime: string
        success: boolean
        error?: string
      }>
      count: number
    }>
  > {
    return await apiClient.get<{
      workflowType: string
      history: Array<{
        stepId: string
        agentName: string
        startTime: string
        endTime: string
        success: boolean
        error?: string
      }>
      count: number
    }>(`/ai/workflow/history?workflowType=${workflowType}`)
  }

  /**
   * 重置工作流
   */
  async resetWorkflow(
    workflowType: string = 'financial_analysis'
  ): Promise<ApiResponse<void>> {
    return await apiClient.post<void>('/ai/workflow/reset', { workflowType })
  }

  /**
   * 工作流健康检查
   */
  async workflowHealthCheck(): Promise<
    ApiResponse<{
      isHealthy: boolean
      status: string
      timestamp: string
    }>
  > {
    return await apiClient.get<{
      isHealthy: boolean
      status: string
      timestamp: string
    }>('/ai/workflow/health')
  }

  /**
   * 根据资产数据生成综合财务分析（使用工作流）
   */
  async generateComprehensiveAnalysis(
    assets: any[],
    modelId: string = 'deepseek-chat'
  ): Promise<ApiResponse<WorkflowExecution>> {
    return await this.executeWorkflow({
      assets,
      modelId,
      workflowType: 'financial_analysis',
    })
  }

  /**
   * 执行特定类型的分析
   */
  async executeSpecificAnalysis(
    analysisType: 'financial' | 'investment' | 'risk' | 'news',
    assets: any[],
    modelId: string = 'deepseek-chat'
  ): Promise<ApiResponse<AgentResult>> {
    const agentMap = {
      financial: 'financial_analysis',
      investment: 'investment_advisor',
      risk: 'risk_assessment',
      news: 'news_analysis',
    }

    return await this.executeAgent({
      agentType: agentMap[analysisType],
      assets,
      modelId,
    })
  }

  /**
   * 执行完整的AI代理工作流
   */
  async executeWorkflow(
    data: ExecuteWorkflowRequest
  ): Promise<ApiResponse<WorkflowExecution>> {
    return await apiClient.post<WorkflowExecution>('/ai/workflow/execute', data)
  }

  /**
   * 执行单个AI代理
   */
  async executeAgent(data: ExecuteAgentRequest): Promise<ApiResponse<AgentResult>> {
    return await apiClient.post<AgentResult>('/ai/agents/execute', data)
  }
}

export const aiService = new AIService()
