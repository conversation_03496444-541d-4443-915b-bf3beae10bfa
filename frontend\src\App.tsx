import React from 'react'
import { <PERSON>rowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom'
import { ConfigProvider } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import { Layout } from './components/layout/Layout'
import { Dashboard } from './pages/dashboard/Dashboard'

// 懒加载页面组件
const AssetsPage = React.lazy(() =>
  import('./pages/assets/AssetsPage').then((module) => ({ default: module.AssetsPage }))
)
const LiabilitiesPage = React.lazy(() =>
  import('./pages/liabilities/LiabilitiesPage').then((module) => ({
    default: module.LiabilitiesPage,
  }))
)
const AIInsightsPage = React.lazy(() =>
  import('./pages/ai-insights/AIInsightsPage').then((module) => ({
    default: module.AIInsightsPage,
  }))
)
const NewsPage = React.lazy(() =>
  import('./pages/news/NewsPage').then((module) => ({ default: module.NewsPage }))
)
const SettingsPage = React.lazy(() =>
  import('./pages/settings/SettingsPage').then((module) => ({
    default: module.SettingsPage,
  }))
)

function App() {
  return (
    <ConfigProvider locale={zhCN}>
      <Router>
        <div className="App">
          <Routes>
            <Route path="/" element={<Layout />}>
              <Route index element={<Dashboard />} />
              <Route
                path="assets"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <AssetsPage />
                  </React.Suspense>
                }
              />
              <Route
                path="liabilities"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <LiabilitiesPage />
                  </React.Suspense>
                }
              />
              <Route
                path="ai-insights"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <AIInsightsPage />
                  </React.Suspense>
                }
              />
              <Route
                path="news"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <NewsPage />
                  </React.Suspense>
                }
              />
              <Route
                path="settings"
                element={
                  <React.Suspense
                    fallback={
                      <div className="flex justify-center items-center h-64">
                        加载中...
                      </div>
                    }
                  >
                    <SettingsPage />
                  </React.Suspense>
                }
              />
            </Route>
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </Router>
    </ConfigProvider>
  )
}

export default App
