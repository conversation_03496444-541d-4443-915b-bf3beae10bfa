import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages'
import { BaseAgent } from './base/BaseAgent'
import { logger } from '../utils/logger'
import type {
  WorkflowState,
  AgentConfig,
  NewsAnalysisResult,
  NewsItem,
  NewsImpact,
} from '../types/agents'
import { getUserAssets, getFinancialNews, analyzeAssetAllocation } from './tools'

export class NewsAnalysisAgent extends BaseAgent {
  constructor(modelId: string = 'deepseek-chat') {
    const config: AgentConfig = {
      name: 'NewsAnalysisAgent',
      description: '专业的新闻分析代理，分析金融新闻对用户投资组合的影响',
      systemPrompt: `你是一位专业的金融新闻分析师，具有敏锐的市场洞察力和新闻解读能力。你的任务是：

1. 收集和筛选相关的金融新闻
2. 分析新闻对不同资产类别的影响
3. 评估市场情绪和趋势变化
4. 识别投资机会和风险信号
5. 提供基于新闻的行动建议

新闻分析维度：
- 宏观经济政策：货币政策、财政政策影响
- 行业动态：特定行业的发展趋势和变化
- 公司新闻：个股相关的重大事件
- 地缘政治：国际关系对市场的影响
- 技术创新：新技术对传统行业的冲击
- 监管变化：法规政策对市场的影响
- 市场情绪：投资者信心和恐慌情绪

请基于新闻内容和用户的投资组合，提供客观、及时的分析和建议。`,
      tools: ['getUserAssets', 'getFinancialNews', 'analyzeAssetAllocation'],
      maxIterations: 3,
      temperature: 0.4,
      modelId,
    }

    super(config)
  }

  protected initializeTools(): void {
    this.tools.set('getUserAssets', getUserAssets)
    this.tools.set('getFinancialNews', getFinancialNews)
    this.tools.set('analyzeAssetAllocation', analyzeAssetAllocation)
  }

  protected async processTask(
    state: WorkflowState,
    messages: BaseMessage[]
  ): Promise<NewsAnalysisResult> {
    try {
      logger.info('开始新闻分析')

      // 1. 获取用户资产数据
      const assetsResult = await this.callTool('getUserAssets', state.userId)
      if (!assetsResult.success) {
        throw new Error('获取用户资产数据失败')
      }

      const assets = assetsResult.output?.data || []

      // 2. 分析资产配置以确定关注重点
      const allocationResult = await this.callTool('analyzeAssetAllocation', assets)
      const assetAllocation = allocationResult.success
        ? allocationResult.output?.data || []
        : []

      // 3. 生成新闻关键词
      const keywords = this.generateNewsKeywords(assets, assetAllocation)

      // 4. 获取相关新闻
      const newsResult = await this.callTool('getFinancialNews', {
        keywords,
        limit: 20,
      })
      if (!newsResult.success) {
        throw new Error('获取新闻数据失败')
      }

      const rawNews = newsResult.output?.data || []

      // 5. 筛选和评分新闻
      const relevantNews = this.filterAndScoreNews(rawNews, assets, keywords)

      // 6. 分析市场情绪
      const marketSentiment = this.analyzeMarketSentiment(relevantNews)

      // 7. 使用AI分析新闻影响
      const newsPrompt = this.buildNewsPrompt({
        assets,
        assetAllocation,
        relevantNews,
        marketSentiment,
        financialAnalysis: state.financialAnalysis,
        investmentAdvice: state.investmentAdvice,
        riskAssessment: state.riskAssessment,
      })

      const newsMessages = [...messages, new HumanMessage(newsPrompt)]
      const aiResponse = await this.generateResponse(newsMessages)

      // 8. 解析AI分析结果
      const { impactAnalysis, actionableInsights } = this.parseNewsAnalysis(
        aiResponse,
        assets
      )

      const result: NewsAnalysisResult = {
        relevantNews,
        marketSentiment,
        impactAnalysis,
        actionableInsights,
        timestamp: new Date(),
      }

      logger.info('新闻分析完成', {
        newsCount: relevantNews.length,
        marketSentiment,
        impactCount: impactAnalysis.length,
      })

      return result
    } catch (error) {
      logger.error('新闻分析失败:', error)
      throw error
    }
  }

  protected async updateState(
    state: WorkflowState,
    result: NewsAnalysisResult
  ): Promise<WorkflowState> {
    return {
      ...state,
      newsAnalysis: result,
      currentStep: 'news_analysis_complete',
      nextSteps: ['coordinator'],
      context: {
        ...state.context,
        newsAnalysisComplete: true,
        marketSentiment: result.marketSentiment,
        newsImpactCount: result.impactAnalysis.length,
      },
    }
  }

  private generateNewsKeywords(assets: any[], assetAllocation: any[]): string[] {
    const keywords: string[] = []

    // 基于资产类别生成关键词
    assetAllocation.forEach((allocation) => {
      switch (allocation.category) {
        case '股票':
          keywords.push('股市', '股票', '上市公司', 'A股', '港股', '美股')
          break
        case '债券':
          keywords.push('债券', '国债', '企业债', '利率', '央行')
          break
        case '房地产':
          keywords.push('房地产', '房价', '楼市', '土地', '房贷')
          break
        case '基金':
          keywords.push('基金', '公募基金', '私募基金', 'ETF')
          break
        case '现金':
          keywords.push('银行', '存款利率', '货币政策')
          break
        case '加密货币':
          keywords.push('比特币', '加密货币', '数字货币', '区块链')
          break
      }
    })

    // 基于具体资产生成关键词
    assets.forEach((asset) => {
      if (asset.name) {
        keywords.push(asset.name)
      }
      if (asset.symbol) {
        keywords.push(asset.symbol)
      }
      if (asset.industry) {
        keywords.push(asset.industry)
      }
    })

    // 添加通用财经关键词
    keywords.push(
      '经济',
      '通胀',
      'GDP',
      '就业',
      '贸易',
      '央行',
      '货币政策',
      '财政政策',
      '监管',
      '市场',
      '投资',
      '金融',
      '风险'
    )

    return [...new Set(keywords)].slice(0, 20) // 去重并限制数量
  }

  private filterAndScoreNews(
    rawNews: any[],
    assets: any[],
    keywords: string[]
  ): NewsItem[] {
    return rawNews
      .map((news) => {
        // 计算相关性评分
        let relevanceScore = 0
        const titleLower = news.title.toLowerCase()
        const summaryLower = news.summary.toLowerCase()

        keywords.forEach((keyword) => {
          const keywordLower = keyword.toLowerCase()
          if (titleLower.includes(keywordLower)) {
            relevanceScore += 0.3
          }
          if (summaryLower.includes(keywordLower)) {
            relevanceScore += 0.1
          }
        })

        // 基于资产匹配度调整评分
        assets.forEach((asset) => {
          if (
            asset.name &&
            (titleLower.includes(asset.name.toLowerCase()) ||
              summaryLower.includes(asset.name.toLowerCase()))
          ) {
            relevanceScore += 0.5
          }
          if (
            asset.symbol &&
            (titleLower.includes(asset.symbol.toLowerCase()) ||
              summaryLower.includes(asset.symbol.toLowerCase()))
          ) {
            relevanceScore += 0.4
          }
        })

        return {
          title: news.title,
          summary: news.summary,
          source: news.source,
          publishedAt: new Date(news.publishedAt),
          relevanceScore: Math.min(1, relevanceScore),
          sentimentScore: news.sentimentScore || 0,
          categories: news.categories || ['财经'],
        }
      })
      .filter((news) => news.relevanceScore > 0.1) // 过滤低相关性新闻
      .sort((a, b) => b.relevanceScore - a.relevanceScore) // 按相关性排序
      .slice(0, 15) // 取前15条最相关的新闻
  }

  private analyzeMarketSentiment(
    news: NewsItem[]
  ): 'positive' | 'neutral' | 'negative' {
    if (news.length === 0) return 'neutral'

    const avgSentiment =
      news.reduce((sum, item) => sum + item.sentimentScore, 0) / news.length

    if (avgSentiment > 0.2) return 'positive'
    if (avgSentiment < -0.2) return 'negative'
    return 'neutral'
  }

  private buildNewsPrompt(data: {
    assets: any[]
    assetAllocation: any[]
    relevantNews: NewsItem[]
    marketSentiment: string
    financialAnalysis?: any
    investmentAdvice?: any
    riskAssessment?: any
  }): string {
    return `
请基于以下新闻信息分析对用户投资组合的影响：

## 用户投资组合
${data.assetAllocation
  .map(
    (item) =>
      `- ${item.category}：${item.percentage?.toFixed(1)}% (¥${
        item.value?.toLocaleString() || '0'
      })`
  )
  .join('\n')}

## 市场情绪
当前市场情绪：${data.marketSentiment} (基于新闻分析)

## 相关新闻 (按相关性排序)
${data.relevantNews
  .map(
    (news, index) =>
      `${index + 1}. ${news.title}
   来源：${news.source} | 发布时间：${news.publishedAt.toLocaleDateString()}
   相关性：${(news.relevanceScore * 100).toFixed(0)}% | 情绪：${
        news.sentimentScore > 0 ? '正面' : news.sentimentScore < 0 ? '负面' : '中性'
      }
   摘要：${news.summary}
   ---`
  )
  .join('\n')}

## 当前财务状况
${
  data.financialAnalysis
    ? `
- 净资产：¥${data.financialAnalysis.netWorth?.toLocaleString() || '0'}
- 财务健康评分：${data.financialAnalysis.financialHealthScore || 0}/100
`
    : '暂无财务分析数据'
}

## 投资策略
${
  data.investmentAdvice
    ? `
- 风险偏好：${data.investmentAdvice.riskProfile}
- 预期收益：${(data.investmentAdvice.expectedReturn * 100).toFixed(1)}%
`
    : '暂无投资建议数据'
}

## 风险评估
${
  data.riskAssessment
    ? `
- 整体风险：${data.riskAssessment.overallRisk}
- 多样化评分：${data.riskAssessment.diversificationScore}/100
`
    : '暂无风险评估数据'
}

请提供：
1. 新闻对各资产类别的影响分析
2. 短期和长期市场趋势判断
3. 具体的投资行动建议
4. 需要关注的风险点
5. 投资组合调整建议

请确保分析客观准确，建议具体可行。
    `.trim()
  }

  private parseNewsAnalysis(
    aiResponse: string,
    assets: any[]
  ): {
    impactAnalysis: NewsImpact[]
    actionableInsights: string[]
  } {
    const impactAnalysis: NewsImpact[] = []
    const actionableInsights: string[] = []

    const lines = aiResponse.split('\n')
    let currentSection = ''

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 识别章节
      if (trimmedLine.includes('影响分析') || trimmedLine.includes('资产影响')) {
        currentSection = 'impact'
        continue
      } else if (
        trimmedLine.includes('建议') ||
        trimmedLine.includes('行动') ||
        trimmedLine.includes('调整')
      ) {
        currentSection = 'insights'
        continue
      }

      // 提取影响分析
      if (
        currentSection === 'impact' &&
        (trimmedLine.startsWith('-') || trimmedLine.match(/^\d+\./))
      ) {
        const impactText = trimmedLine.replace(/^[-\d.]\s*/, '').trim()

        // 尝试解析资产和影响
        assets.forEach((asset) => {
          if (
            impactText.toLowerCase().includes(asset.name?.toLowerCase() || '') ||
            impactText.toLowerCase().includes(asset.categoryId?.toLowerCase() || '')
          ) {
            let impact: 'positive' | 'negative' | 'neutral' = 'neutral'
            if (
              impactText.includes('利好') ||
              impactText.includes('上涨') ||
              impactText.includes('积极')
            ) {
              impact = 'positive'
            } else if (
              impactText.includes('利空') ||
              impactText.includes('下跌') ||
              impactText.includes('负面')
            ) {
              impact = 'negative'
            }

            impactAnalysis.push({
              asset: asset.name || asset.categoryId,
              impact,
              confidence: 0.7, // 默认置信度
              reasoning: impactText,
            })
          }
        })
      }

      // 提取行动建议
      if (
        currentSection === 'insights' &&
        (trimmedLine.startsWith('-') || trimmedLine.match(/^\d+\./))
      ) {
        const insight = trimmedLine.replace(/^[-\d.]\s*/, '').trim()
        if (insight.length > 10) {
          actionableInsights.push(insight)
        }
      }
    }

    // 如果没有提取到建议，生成默认建议
    if (actionableInsights.length === 0) {
      actionableInsights.push(
        '密切关注市场动态和相关新闻',
        '根据新闻影响适时调整投资组合',
        '保持理性投资，避免情绪化决策',
        '关注政策变化对投资的长期影响',
        '定期评估新闻对投资策略的影响'
      )
    }

    return {
      impactAnalysis: impactAnalysis.slice(0, 10), // 限制数量
      actionableInsights: actionableInsights.slice(0, 8),
    }
  }
}
