import { ChatOpenAI } from '@langchain/openai'
import { ChatAnthropic } from '@langchain/anthropic'
import { db } from '../database/connection'
import { aiConversations, aiMessages, aiModels, userSettings } from '../database/schema'
import { eq, and, desc } from 'drizzle-orm'
import { logger } from '../utils/logger'
import type { AIConversation, AIMessage, AIModel } from '../types'
import { v4 as uuidv4 } from 'uuid'
import crypto from 'crypto'
import { workflowService } from './workflowService'
import type { WorkflowExecutionResult } from '../types/agents'

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY || 'your-encryption-key-32-chars-long'

export class AIService {
  /**
   * 加密API Key
   */
  private encryptApiKey(apiKey: string): string {
    const cipher = crypto.createCipher('aes-256-cbc', ENCRYPTION_KEY)
    let encrypted = cipher.update(apiKey, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    return encrypted
  }

  /**
   * 解密API Key
   */
  private decryptApiKey(encryptedApiKey: string): string {
    const decipher = crypto.createDecipher('aes-256-cbc', ENCRYPTION_KEY)
    let decrypted = decipher.update(encryptedApiKey, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    return decrypted
  }

  /**
   * 获取用户的AI配置
   */
  private async getUserAIConfig(userId: string): Promise<Record<string, string>> {
    try {
      const settings = await db()
        .select()
        .from(userSettings)
        .where(eq(userSettings.userId, userId))
        .limit(1)

      if (!settings[0] || !settings[0].aiApiKeys) {
        return {}
      }

      // 解密API Keys
      const encryptedKeys = JSON.parse(settings[0].aiApiKeys)
      const decryptedKeys: Record<string, string> = {}

      for (const [provider, encryptedKey] of Object.entries(encryptedKeys)) {
        try {
          decryptedKeys[provider] = this.decryptApiKey(encryptedKey as string)
        } catch (error) {
          logger.warn(`解密${provider} API Key失败:`, error)
        }
      }

      return decryptedKeys
    } catch (error) {
      logger.error('获取用户AI配置失败:', error)
      return {}
    }
  }

  /**
   * 获取AI模型实例
   */
  private async getAIModel(userId: string, modelId: string) {
    const apiKeys = await this.getUserAIConfig(userId)

    // 获取模型信息
    const model = await db()
      .select()
      .from(aiModels)
      .where(eq(aiModels.id, modelId))
      .limit(1)

    if (!model[0]) {
      throw new Error('AI模型不存在')
    }

    const modelInfo = model[0]
    const apiKey = apiKeys[modelInfo.provider]

    if (!apiKey) {
      throw new Error(`缺少${modelInfo.provider}的API Key`)
    }

    // 根据提供商返回相应的模型实例
    switch (modelInfo.provider) {
      case 'openai':
        return new ChatOpenAI({
          modelName: modelInfo.modelId,
          openAIApiKey: apiKey,
        })
      case 'anthropic':
        return new ChatAnthropic({
          modelName: modelInfo.modelId,
          anthropicApiKey: apiKey,
        })
      case 'deepseek':
        // DeepSeek使用OpenAI兼容的API
        return new ChatOpenAI({
          modelName: modelInfo.modelId,
          openAIApiKey: apiKey,
          configuration: {
            baseURL: 'https://api.deepseek.com/v1',
          },
        })
      default:
        throw new Error(`不支持的AI提供商: ${modelInfo.provider}`)
    }
  }

  /**
   * 创建新对话
   */
  async createConversation(
    userId: string,
    title: string,
    contextType?: string,
    contextData?: any
  ): Promise<AIConversation> {
    try {
      const conversation = await db()
        .insert(aiConversations)
        .values({
          id: uuidv4(),
          userId,
          title,
          contextType: contextType || null,
          contextData: contextData ? JSON.stringify(contextData) : null,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning()

      if (!conversation[0]) {
        throw new Error('对话创建失败')
      }

      logger.info(`AI对话创建成功: ${conversation[0].id}`)
      return conversation[0]
    } catch (error) {
      logger.error('创建AI对话失败:', error)
      throw error
    }
  }

  /**
   * 获取用户对话列表
   */
  async getUserConversations(userId: string): Promise<AIConversation[]> {
    try {
      const conversations = await db()
        .select()
        .from(aiConversations)
        .where(eq(aiConversations.userId, userId))
        .orderBy(desc(aiConversations.updatedAt))

      return conversations
    } catch (error) {
      logger.error('获取用户对话失败:', error)
      throw error
    }
  }

  /**
   * 获取对话消息
   */
  async getConversationMessages(
    userId: string,
    conversationId: string
  ): Promise<AIMessage[]> {
    try {
      // 验证对话是否属于用户
      const conversation = await db()
        .select()
        .from(aiConversations)
        .where(
          and(
            eq(aiConversations.id, conversationId),
            eq(aiConversations.userId, userId)
          )
        )
        .limit(1)

      if (!conversation[0]) {
        throw new Error('对话不存在或无权限')
      }

      const messages = await db()
        .select()
        .from(aiMessages)
        .where(eq(aiMessages.conversationId, conversationId))
        .orderBy(aiMessages.createdAt)

      return messages
    } catch (error) {
      logger.error('获取对话消息失败:', error)
      throw error
    }
  }

  /**
   * 发送消息并获取AI回复
   */
  async sendMessage(
    userId: string,
    conversationId: string,
    content: string,
    modelId: string
  ): Promise<{ userMessage: AIMessage; aiMessage: AIMessage }> {
    try {
      // 验证对话权限
      const conversation = await db()
        .select()
        .from(aiConversations)
        .where(
          and(
            eq(aiConversations.id, conversationId),
            eq(aiConversations.userId, userId)
          )
        )
        .limit(1)

      if (!conversation[0]) {
        throw new Error('对话不存在或无权限')
      }

      // 保存用户消息
      const userMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'user',
          content,
          createdAt: new Date(),
        })
        .returning()

      if (!userMessage[0]) {
        throw new Error('用户消息保存失败')
      }

      // 获取对话历史
      const messages = await this.getConversationMessages(userId, conversationId)

      // 构建消息历史（排除当前用户消息，因为已经包含在messages中）
      const messageHistory = messages.map((msg) => {
        const role = msg.role as 'user' | 'assistant' | 'system'
        if (role === 'user') {
          return { type: 'human' as const, content: msg.content }
        } else if (role === 'assistant') {
          return { type: 'ai' as const, content: msg.content }
        } else {
          return { type: 'system' as const, content: msg.content }
        }
      })

      // 获取AI模型
      const model = await this.getAIModel(userId, modelId)

      // 生成AI回复
      const result = await model.invoke(messageHistory)

      // 保存AI回复
      const tokensUsed = result.response_metadata?.tokenUsage?.totalTokens || 0
      const aiContent =
        typeof result.content === 'string'
          ? result.content
          : JSON.stringify(result.content)
      const aiMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'assistant',
          content: aiContent,
          tokensUsed,
          cost: await this.calculateCost(modelId, tokensUsed),
          createdAt: new Date(),
        })
        .returning()

      if (!aiMessage[0]) {
        throw new Error('AI消息保存失败')
      }

      // 更新对话时间
      await db()
        .update(aiConversations)
        .set({ updatedAt: new Date() })
        .where(eq(aiConversations.id, conversationId))

      logger.info(`AI消息发送成功: ${conversationId}`)

      return {
        userMessage: userMessage[0],
        aiMessage: aiMessage[0],
      }
    } catch (error) {
      logger.error('发送AI消息失败:', error)
      throw error
    }
  }

  /**
   * 流式发送消息
   */
  async sendMessageStream(
    userId: string,
    conversationId: string,
    content: string,
    modelId: string
  ) {
    try {
      // 验证对话权限
      const conversation = await db()
        .select()
        .from(aiConversations)
        .where(
          and(
            eq(aiConversations.id, conversationId),
            eq(aiConversations.userId, userId)
          )
        )
        .limit(1)

      if (!conversation[0]) {
        throw new Error('对话不存在或无权限')
      }

      // 保存用户消息
      const userMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'user',
          content,
          createdAt: new Date(),
        })
        .returning()

      if (!userMessage[0]) {
        throw new Error('用户消息保存失败')
      }

      // 获取对话历史
      const messages = await this.getConversationMessages(userId, conversationId)

      // 构建消息历史
      const messageHistory = messages.map((msg) => {
        const role = msg.role as 'user' | 'assistant' | 'system'
        if (role === 'user') {
          return { type: 'human' as const, content: msg.content }
        } else if (role === 'assistant') {
          return { type: 'ai' as const, content: msg.content }
        } else {
          return { type: 'system' as const, content: msg.content }
        }
      })

      // 获取AI模型
      const model = await this.getAIModel(userId, modelId)

      // 创建流式响应
      const result = await model.stream(messageHistory)

      return {
        userMessage: userMessage[0],
        stream: result,
        finishReason: 'stop',
        usage: { totalTokens: 0 },
      }
    } catch (error) {
      logger.error('流式发送AI消息失败:', error)
      throw error
    }
  }

  /**
   * 保存流式消息的最终结果
   */
  async saveStreamedMessage(
    conversationId: string,
    modelId: string,
    content: string,
    tokensUsed?: number
  ): Promise<AIMessage> {
    try {
      const aiMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'assistant',
          content,
          tokensUsed: tokensUsed || null,
          cost: await this.calculateCost(modelId, tokensUsed || 0),
          createdAt: new Date(),
        })
        .returning()

      if (!aiMessage[0]) {
        throw new Error('AI消息保存失败')
      }

      // 更新对话时间
      await db()
        .update(aiConversations)
        .set({ updatedAt: new Date() })
        .where(eq(aiConversations.id, conversationId))

      return aiMessage[0]
    } catch (error) {
      logger.error('保存流式消息失败:', error)
      throw error
    }
  }

  /**
   * 计算使用成本
   */
  private async calculateCost(modelId: string, tokensUsed: number): Promise<number> {
    try {
      const model = await db()
        .select()
        .from(aiModels)
        .where(eq(aiModels.id, modelId))
        .limit(1)

      if (!model[0] || !model[0].costPerToken) {
        return 0
      }

      return tokensUsed * model[0].costPerToken
    } catch (error) {
      logger.error('计算成本失败:', error)
      return 0
    }
  }

  /**
   * 获取可用的AI模型
   */
  async getAvailableModels(): Promise<AIModel[]> {
    try {
      const models = await db()
        .select()
        .from(aiModels)
        .where(eq(aiModels.isActive, true))

      return models
    } catch (error) {
      logger.error('获取AI模型失败:', error)
      throw error
    }
  }

  /**
   * 删除对话
   */
  async deleteConversation(userId: string, conversationId: string): Promise<void> {
    try {
      // 验证对话权限
      const conversation = await db()
        .select()
        .from(aiConversations)
        .where(
          and(
            eq(aiConversations.id, conversationId),
            eq(aiConversations.userId, userId)
          )
        )
        .limit(1)

      if (!conversation[0]) {
        throw new Error('对话不存在或无权限')
      }

      await db().delete(aiConversations).where(eq(aiConversations.id, conversationId))

      logger.info(`AI对话删除成功: ${conversationId}`)
    } catch (error) {
      logger.error('删除AI对话失败:', error)
      throw error
    }
  }

  /**
   * 更新对话标题
   */
  async updateConversationTitle(
    userId: string,
    conversationId: string,
    title: string
  ): Promise<void> {
    try {
      // 验证对话权限
      const conversation = await db()
        .select()
        .from(aiConversations)
        .where(
          and(
            eq(aiConversations.id, conversationId),
            eq(aiConversations.userId, userId)
          )
        )
        .limit(1)

      if (!conversation[0]) {
        throw new Error('对话不存在或无权限')
      }

      await db()
        .update(aiConversations)
        .set({
          title,
          updatedAt: new Date(),
        })
        .where(eq(aiConversations.id, conversationId))

      logger.info(`对话标题更新成功: ${conversationId}`)
    } catch (error) {
      logger.error('更新对话标题失败:', error)
      throw error
    }
  }

  /**
   * 执行AI代理工作流
   */
  async executeWorkflow(
    userId: string,
    conversationId: string,
    content: string,
    modelId: string,
    workflowType: string = 'financial_analysis'
  ): Promise<{
    workflowResult: WorkflowExecutionResult
    conversation: AIConversation
    userMessage: AIMessage
    aiMessage: AIMessage
  }> {
    try {
      logger.info('开始执行AI代理工作流', { userId, conversationId, workflowType })

      // 验证对话权限
      const conversation = await db()
        .select()
        .from(aiConversations)
        .where(
          and(
            eq(aiConversations.id, conversationId),
            eq(aiConversations.userId, userId)
          )
        )
        .limit(1)

      if (!conversation[0]) {
        throw new Error('对话不存在或无权限')
      }

      // 保存用户消息
      const userMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'user',
          content,
          createdAt: new Date(),
        })
        .returning()

      if (!userMessage[0]) {
        throw new Error('用户消息保存失败')
      }

      // 执行工作流
      const workflowResult = await workflowService.executeFinancialWorkflow({
        userId,
        conversationId,
        userQuery: content,
        modelId,
      })

      // 生成AI回复内容
      let aiContent = ''
      if (workflowResult.success && workflowResult.state.context?.comprehensiveAdvice) {
        const advice = workflowResult.state.context.comprehensiveAdvice
        aiContent = this.formatWorkflowResponse(advice)
      } else {
        aiContent = workflowResult.error || '工作流执行失败，请稍后重试'
      }

      // 保存AI回复
      const aiMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'assistant',
          content: aiContent,
          metadata: JSON.stringify({
            workflowType,
            executionTime: workflowResult.executionTime,
            stepsCount: workflowResult.steps.length,
            success: workflowResult.success,
          }),
          createdAt: new Date(),
        })
        .returning()

      if (!aiMessage[0]) {
        throw new Error('AI消息保存失败')
      }

      // 更新对话时间
      await db()
        .update(aiConversations)
        .set({ updatedAt: new Date() })
        .where(eq(aiConversations.id, conversationId))

      logger.info('AI代理工作流执行完成', {
        conversationId,
        success: workflowResult.success,
        executionTime: workflowResult.executionTime,
      })

      return {
        workflowResult,
        conversation: conversation[0],
        userMessage: userMessage[0],
        aiMessage: aiMessage[0],
      }
    } catch (error) {
      logger.error('执行AI代理工作流失败:', error)
      throw error
    }
  }

  /**
   * 执行单个AI代理
   */
  async executeSingleAgent(
    userId: string,
    conversationId: string,
    content: string,
    modelId: string,
    agentName: string
  ): Promise<{
    workflowResult: WorkflowExecutionResult
    userMessage: AIMessage
    aiMessage: AIMessage
  }> {
    try {
      logger.info(`开始执行单个AI代理: ${agentName}`)

      // 验证对话权限
      const conversation = await db()
        .select()
        .from(aiConversations)
        .where(
          and(
            eq(aiConversations.id, conversationId),
            eq(aiConversations.userId, userId)
          )
        )
        .limit(1)

      if (!conversation[0]) {
        throw new Error('对话不存在或无权限')
      }

      // 保存用户消息
      const userMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'user',
          content,
          createdAt: new Date(),
        })
        .returning()

      if (!userMessage[0]) {
        throw new Error('用户消息保存失败')
      }

      // 执行单个代理
      const workflowResult = await workflowService.executeSingleAgent({
        agentName,
        userId,
        conversationId,
        userQuery: content,
        modelId,
      })

      // 生成AI回复内容
      let aiContent = ''
      if (workflowResult.success) {
        aiContent = this.formatSingleAgentResponse(agentName, workflowResult.state)
      } else {
        aiContent = workflowResult.error || `${agentName}代理执行失败，请稍后重试`
      }

      // 保存AI回复
      const aiMessage = await db()
        .insert(aiMessages)
        .values({
          id: uuidv4(),
          conversationId,
          modelId,
          role: 'assistant',
          content: aiContent,
          metadata: JSON.stringify({
            agentName,
            executionTime: workflowResult.executionTime,
            success: workflowResult.success,
          }),
          createdAt: new Date(),
        })
        .returning()

      if (!aiMessage[0]) {
        throw new Error('AI消息保存失败')
      }

      // 更新对话时间
      await db()
        .update(aiConversations)
        .set({ updatedAt: new Date() })
        .where(eq(aiConversations.id, conversationId))

      logger.info(`单个AI代理 ${agentName} 执行完成`)

      return {
        workflowResult,
        userMessage: userMessage[0],
        aiMessage: aiMessage[0],
      }
    } catch (error) {
      logger.error(`执行单个AI代理 ${agentName} 失败:`, error)
      throw error
    }
  }

  /**
   * 格式化工作流响应
   */
  private formatWorkflowResponse(advice: any): string {
    if (!advice) return '分析完成，但未能生成建议'

    let response = ''

    // 添加综合评估
    if (advice.summary) {
      response += `## 综合评估\n${advice.summary}\n\n`
    }

    // 添加优先级建议
    if (advice.prioritizedRecommendations) {
      const { immediate, shortTerm, mediumTerm, longTerm } =
        advice.prioritizedRecommendations

      if (immediate && immediate.length > 0) {
        response += `## 立即执行建议\n${immediate
          .map((item: string) => `• ${item}`)
          .join('\n')}\n\n`
      }

      if (shortTerm && shortTerm.length > 0) {
        response += `## 短期规划建议\n${shortTerm
          .map((item: string) => `• ${item}`)
          .join('\n')}\n\n`
      }

      if (mediumTerm && mediumTerm.length > 0) {
        response += `## 中期规划建议\n${mediumTerm
          .map((item: string) => `• ${item}`)
          .join('\n')}\n\n`
      }

      if (longTerm && longTerm.length > 0) {
        response += `## 长期规划建议\n${longTerm
          .map((item: string) => `• ${item}`)
          .join('\n')}\n\n`
      }
    }

    // 添加风险提示
    if (advice.riskWarnings && advice.riskWarnings.length > 0) {
      response += `## 风险提示\n${advice.riskWarnings
        .map((item: string) => `⚠️ ${item}`)
        .join('\n')}\n\n`
    }

    // 添加执行计划
    if (advice.executionPlan && advice.executionPlan.length > 0) {
      response += `## 执行计划\n${advice.executionPlan
        .map((item: string) => `📋 ${item}`)
        .join('\n')}\n\n`
    }

    return response || '分析完成，请查看详细结果'
  }

  /**
   * 格式化单个代理响应
   */
  private formatSingleAgentResponse(agentName: string, state: any): string {
    let response = `## ${this.getAgentDisplayName(agentName)}分析结果\n\n`

    switch (agentName) {
      case 'financial_analysis':
        if (state.financialAnalysis) {
          const fa = state.financialAnalysis
          response += `**财务概况**\n`
          response += `• 总资产：¥${fa.totalAssets?.toLocaleString() || '0'}\n`
          response += `• 总负债：¥${fa.totalDebts?.toLocaleString() || '0'}\n`
          response += `• 净资产：¥${fa.netWorth?.toLocaleString() || '0'}\n`
          response += `• 财务健康评分：${fa.financialHealthScore || 0}/100\n\n`

          if (fa.recommendations && fa.recommendations.length > 0) {
            response += `**建议**\n${fa.recommendations
              .map((rec: string) => `• ${rec}`)
              .join('\n')}`
          }
        }
        break

      case 'investment_advisor':
        if (state.investmentAdvice) {
          const ia = state.investmentAdvice
          response += `**投资建议**\n`
          response += `• 风险偏好：${ia.riskProfile}\n`
          response += `• 预期收益：${(ia.expectedReturn * 100).toFixed(1)}%\n`
          response += `• 投资周期：${ia.timeHorizon}\n\n`

          if (ia.specificRecommendations && ia.specificRecommendations.length > 0) {
            response += `**具体建议**\n${ia.specificRecommendations
              .map((rec: any) => `• ${rec.action} ${rec.asset}: ${rec.reason}`)
              .join('\n')}`
          }
        }
        break

      case 'risk_assessment':
        if (state.riskAssessment) {
          const ra = state.riskAssessment
          response += `**风险评估**\n`
          response += `• 整体风险：${ra.overallRisk}\n`
          response += `• 多样化评分：${ra.diversificationScore}/100\n\n`

          if (ra.riskFactors && ra.riskFactors.length > 0) {
            response += `**风险因素**\n${ra.riskFactors
              .map((risk: any) => `• ${risk.type} (${risk.level}): ${risk.description}`)
              .join('\n')}\n\n`
          }

          if (ra.recommendations && ra.recommendations.length > 0) {
            response += `**风险控制建议**\n${ra.recommendations
              .map((rec: string) => `• ${rec}`)
              .join('\n')}`
          }
        }
        break

      case 'news_analysis':
        if (state.newsAnalysis) {
          const na = state.newsAnalysis
          response += `**新闻分析**\n`
          response += `• 市场情绪：${na.marketSentiment}\n`
          response += `• 相关新闻：${na.relevantNews?.length || 0}条\n\n`

          if (na.actionableInsights && na.actionableInsights.length > 0) {
            response += `**行动建议**\n${na.actionableInsights
              .map((insight: string) => `• ${insight}`)
              .join('\n')}`
          }
        }
        break

      default:
        response += '分析完成，请查看详细结果'
    }

    return response
  }

  /**
   * 获取代理显示名称
   */
  private getAgentDisplayName(agentName: string): string {
    const displayNames: Record<string, string> = {
      financial_analysis: '财务分析',
      investment_advisor: '投资建议',
      risk_assessment: '风险评估',
      news_analysis: '新闻分析',
      coordinator: '综合协调',
    }

    return displayNames[agentName] || agentName
  }

  /**
   * 获取工作流状态
   */
  async getWorkflowStatus(): Promise<any> {
    try {
      return await workflowService.getWorkflowStatus()
    } catch (error) {
      logger.error('获取工作流状态失败:', error)
      throw error
    }
  }
}

export const aiService = new AIService()
