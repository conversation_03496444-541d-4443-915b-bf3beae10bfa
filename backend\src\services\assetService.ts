import { db } from '../database/connection'
import { assets, assetCategories, assetHistory } from '../database/schema'
import { eq, and, desc } from 'drizzle-orm'
import { logger } from '../utils/logger'
import type { Asset, AssetCategory, AssetHistory } from '../types'
import { v4 as uuidv4 } from 'uuid'

export class AssetService {
  /**
   * 获取所有资产
   */
  async getAllAssets(): Promise<Asset[]> {
    try {
      const allAssets = await db().select().from(assets).orderBy(desc(assets.updatedAt))

      logger.info(`获取资产成功, 数量: ${allAssets.length}`)
      return allAssets
    } catch (error) {
      logger.error('获取资产失败:', error)
      throw error
    }
  }

  /**
   * 根据分类获取资产
   */
  async getAssetsByCategory(categoryId: string): Promise<Asset[]> {
    try {
      const categoryAssets = await db()
        .select()
        .from(assets)
        .where(eq(assets.categoryId, categoryId))
        .orderBy(desc(assets.updatedAt))

      return categoryAssets
    } catch (error) {
      logger.error('根据分类获取资产失败:', error)
      throw error
    }
  }

  /**
   * 获取单个资产详情
   */
  async getAssetById(assetId: string): Promise<Asset | null> {
    try {
      const asset = await db()
        .select()
        .from(assets)
        .where(eq(assets.id, assetId))
        .limit(1)

      return asset[0] || null
    } catch (error) {
      logger.error('获取资产详情失败:', error)
      throw error
    }
  }

  /**
   * 创建新资产
   */
  async createAsset(
    assetData: Omit<Asset, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Asset> {
    try {
      // 验证资产分类是否存在
      const categoryExists = await db()
        .select({ id: assetCategories.id })
        .from(assetCategories)
        .where(eq(assetCategories.id, assetData.categoryId))
        .limit(1)

      if (!categoryExists.length) {
        throw new Error(`资产分类不存在: ${assetData.categoryId}`)
      }

      const newAsset = await db()
        .insert(assets)
        .values({
          id: uuidv4(),
          ...assetData,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning()

      if (!newAsset[0]) {
        throw new Error('资产创建失败')
      }

      // 创建初始历史记录
      await this.addAssetHistory(newAsset[0].id, newAsset[0].currentValue)

      logger.info(`资产创建成功: ${newAsset[0].id}`)
      return newAsset[0]
    } catch (error) {
      logger.error('创建资产失败:', error)
      throw error
    }
  }

  /**
   * 更新资产
   */
  async updateAsset(assetId: string, updateData: Partial<Asset>): Promise<Asset> {
    try {
      // 检查资产是否存在
      const existingAsset = await this.getAssetById(assetId)
      if (!existingAsset) {
        throw new Error('资产不存在或无权限')
      }

      const updatedAsset = await db()
        .update(assets)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(assets.id, assetId))
        .returning()

      if (!updatedAsset[0]) {
        throw new Error('资产更新失败')
      }

      // 如果更新了价值，添加历史记录
      if (
        updateData.currentValue &&
        updateData.currentValue !== existingAsset.currentValue
      ) {
        await this.addAssetHistory(assetId, updateData.currentValue)
      }

      logger.info(`资产更新成功: ${assetId}`)
      return updatedAsset[0]
    } catch (error) {
      logger.error('更新资产失败:', error)
      throw error
    }
  }

  /**
   * 删除资产
   */
  async deleteAsset(assetId: string): Promise<void> {
    try {
      // 检查资产是否存在
      const existingAsset = await this.getAssetById(assetId)
      if (!existingAsset) {
        throw new Error('资产不存在')
      }

      await db().delete(assets).where(eq(assets.id, assetId))

      logger.info(`资产删除成功: ${assetId}`)
    } catch (error) {
      logger.error('删除资产失败:', error)
      throw error
    }
  }

  /**
   * 获取所有资产分类
   */
  async getAssetCategories(): Promise<AssetCategory[]> {
    try {
      const categories = await db().select().from(assetCategories)

      return categories
    } catch (error) {
      logger.error('获取资产分类失败:', error)
      throw error
    }
  }

  /**
   * 获取资产历史记录
   */
  async getAssetHistory(assetId: string, limit: number = 30): Promise<AssetHistory[]> {
    try {
      // 检查资产是否存在
      const asset = await this.getAssetById(assetId)
      if (!asset) {
        throw new Error('资产不存在')
      }

      const history = await db()
        .select()
        .from(assetHistory)
        .where(eq(assetHistory.assetId, assetId))
        .orderBy(desc(assetHistory.recordDate))
        .limit(limit)

      return history
    } catch (error) {
      logger.error('获取资产历史失败:', error)
      throw error
    }
  }

  /**
   * 添加资产历史记录
   */
  async addAssetHistory(
    assetId: string,
    value: number,
    recordDate?: string
  ): Promise<AssetHistory> {
    try {
      const historyId: string = uuidv4()
      const now = new Date()
      const finalRecordDate = recordDate ? recordDate : now.toISOString().split('T')[0]

      const historyRecord = await db()
        .insert(assetHistory)
        .values({
          id: historyId,
          assetId: assetId,
          value: value,
          recordDate: finalRecordDate as string,
          createdAt: now,
        })
        .returning()

      if (!historyRecord[0]) {
        throw new Error('历史记录创建失败')
      }

      return historyRecord[0]
    } catch (error) {
      logger.error('添加资产历史失败:', error)
      throw error
    }
  }

  /**
   * 获取资产统计
   */
  async getAssetStats(): Promise<{
    totalValue: number
    totalAssets: number
    categoryBreakdown: Array<{
      categoryId: string
      categoryName: string
      totalValue: number
      assetCount: number
      percentage: number
    }>
  }> {
    try {
      const userAssets = await this.getAllAssets()
      const categories = await this.getAssetCategories()

      const totalValue = userAssets.reduce((sum, asset) => sum + asset.currentValue, 0)
      const totalAssets = userAssets.length

      // 按分类统计
      const categoryMap = new Map<
        string,
        { name: string; totalValue: number; assetCount: number }
      >()

      categories.forEach((category) => {
        categoryMap.set(category.id, {
          name: category.name,
          totalValue: 0,
          assetCount: 0,
        })
      })

      userAssets.forEach((asset) => {
        const categoryData = categoryMap.get(asset.categoryId)
        if (categoryData) {
          categoryData.totalValue += asset.currentValue
          categoryData.assetCount += 1
        }
      })

      const categoryBreakdown = Array.from(categoryMap.entries()).map(
        ([categoryId, data]) => ({
          categoryId,
          categoryName: data.name,
          totalValue: data.totalValue,
          assetCount: data.assetCount,
          percentage: totalValue > 0 ? (data.totalValue / totalValue) * 100 : 0,
        })
      )

      return {
        totalValue,
        totalAssets,
        categoryBreakdown,
      }
    } catch (error) {
      logger.error('获取用户资产统计失败:', error)
      throw error
    }
  }

  /**
   * 批量更新资产价值
   */
  async batchUpdateAssetValues(
    updates: Array<{ assetId: string; newValue: number }>
  ): Promise<void> {
    try {
      for (const update of updates) {
        await this.updateAsset(update.assetId, {
          currentValue: update.newValue,
        })
      }

      logger.info(`批量更新资产价值成功, 更新数量: ${updates.length}`)
    } catch (error) {
      logger.error('批量更新资产价值失败:', error)
      throw error
    }
  }

  /**
   * 搜索资产
   */
  async searchAssets(query: string): Promise<Asset[]> {
    try {
      const userAssets = await this.getAllAssets()

      // 简单的文本搜索
      const filteredAssets = userAssets.filter(
        (asset) =>
          asset.name.toLowerCase().includes(query.toLowerCase()) ||
          (asset.description &&
            asset.description.toLowerCase().includes(query.toLowerCase()))
      )

      return filteredAssets
    } catch (error) {
      logger.error('搜索资产失败:', error)
      throw error
    }
  }
}

export const assetService = new AssetService()
