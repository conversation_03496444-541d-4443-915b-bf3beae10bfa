import {
  BaseMessage,
  HumanMessage,
  AIMessage,
  SystemMessage,
} from '@langchain/core/messages'
import { ChatOpenAI } from '@langchain/openai'
import { ChatAnthropic } from '@langchain/anthropic'
import { logger } from '../../utils/logger'
import type { WorkflowState, AgentConfig, ToolCallResult } from '../../types/agents'

export abstract class BaseAgent {
  protected config: AgentConfig
  protected model: ChatOpenAI | ChatAnthropic
  protected tools: Map<string, Function> = new Map()

  constructor(config: AgentConfig) {
    this.config = config
    this.model = this.initializeModel()
    this.initializeTools()
  }

  /**
   * 初始化AI模型
   */
  private initializeModel(): ChatOpenAI | ChatAnthropic {
    const { modelId, temperature } = this.config

    // 根据模型ID判断使用哪个提供商
    if (modelId.includes('gpt') || modelId.includes('deepseek')) {
      return new ChatOpenAI({
        modelName: modelId,
        temperature,
        maxTokens: 2000,
      })
    } else if (modelId.includes('claude')) {
      return new ChatAnthropic({
        modelName: modelId,
        temperature,
        maxTokens: 2000,
      })
    } else {
      // 默认使用OpenAI
      return new ChatOpenAI({
        modelName: 'gpt-3.5-turbo',
        temperature,
        maxTokens: 2000,
      })
    }
  }

  /**
   * 初始化工具
   */
  protected abstract initializeTools(): void

  /**
   * 执行代理任务
   */
  async execute(state: WorkflowState): Promise<WorkflowState> {
    try {
      logger.info(`执行代理: ${this.config.name}`)

      // 构建消息历史
      const messages = this.buildMessages(state)

      // 执行主要逻辑
      const result = await this.processTask(state, messages)

      // 更新状态
      const updatedState = await this.updateState(state, result)

      logger.info(`代理 ${this.config.name} 执行完成`)
      return updatedState
    } catch (error) {
      logger.error(`代理 ${this.config.name} 执行失败:`, error)
      return {
        ...state,
        error: error instanceof Error ? error.message : '代理执行失败',
        currentStep: 'error',
      }
    }
  }

  /**
   * 构建消息历史
   */
  protected buildMessages(state: WorkflowState): BaseMessage[] {
    const messages: BaseMessage[] = []

    // 添加系统提示
    messages.push(new SystemMessage(this.config.systemPrompt))

    // 添加上下文信息
    const contextMessage = this.buildContextMessage(state)
    if (contextMessage) {
      messages.push(contextMessage)
    }

    // 添加历史消息
    messages.push(...state.messages)

    // 添加当前用户查询
    if (state.userQuery) {
      messages.push(new HumanMessage(state.userQuery))
    }

    return messages
  }

  /**
   * 构建上下文消息
   */
  protected buildContextMessage(state: WorkflowState): BaseMessage | null {
    const contextParts: string[] = []

    // 添加用户资产信息
    if (state.userAssets && state.userAssets.length > 0) {
      contextParts.push(`用户资产信息：\n${JSON.stringify(state.userAssets, null, 2)}`)
    }

    // 添加用户债务信息
    if (state.userDebts && state.userDebts.length > 0) {
      contextParts.push(`用户债务信息：\n${JSON.stringify(state.userDebts, null, 2)}`)
    }

    // 添加用户档案
    if (state.userProfile) {
      contextParts.push(`用户档案：\n${JSON.stringify(state.userProfile, null, 2)}`)
    }

    // 添加之前的分析结果
    if (state.financialAnalysis) {
      contextParts.push(
        `财务分析结果：\n${JSON.stringify(state.financialAnalysis, null, 2)}`
      )
    }

    if (state.investmentAdvice) {
      contextParts.push(
        `投资建议结果：\n${JSON.stringify(state.investmentAdvice, null, 2)}`
      )
    }

    if (state.riskAssessment) {
      contextParts.push(
        `风险评估结果：\n${JSON.stringify(state.riskAssessment, null, 2)}`
      )
    }

    if (state.newsAnalysis) {
      contextParts.push(
        `新闻分析结果：\n${JSON.stringify(state.newsAnalysis, null, 2)}`
      )
    }

    if (contextParts.length === 0) {
      return null
    }

    return new SystemMessage(`上下文信息：\n\n${contextParts.join('\n\n')}`)
  }

  /**
   * 处理具体任务 - 由子类实现
   */
  protected abstract processTask(
    state: WorkflowState,
    messages: BaseMessage[]
  ): Promise<any>

  /**
   * 更新状态 - 由子类实现
   */
  protected abstract updateState(
    state: WorkflowState,
    result: any
  ): Promise<WorkflowState>

  /**
   * 调用工具
   */
  protected async callTool(toolName: string, input: any): Promise<ToolCallResult> {
    const startTime = Date.now()

    try {
      const tool = this.tools.get(toolName)
      if (!tool) {
        throw new Error(`工具 ${toolName} 不存在`)
      }

      const output = await tool(input)
      const executionTime = Date.now() - startTime

      return {
        toolName,
        input,
        output,
        success: true,
        executionTime,
      }
    } catch (error) {
      const executionTime = Date.now() - startTime

      return {
        toolName,
        input,
        output: null,
        success: false,
        error: error instanceof Error ? error.message : '工具调用失败',
        executionTime,
      }
    }
  }

  /**
   * 生成AI回复
   */
  protected async generateResponse(messages: BaseMessage[]): Promise<string> {
    try {
      const response = await this.model.invoke(messages)
      return response.content as string
    } catch (error) {
      logger.error(`生成AI回复失败:`, error)
      throw error
    }
  }

  /**
   * 解析结构化输出
   */
  protected parseStructuredOutput<T>(content: string, schema: any): T | null {
    try {
      // 尝试提取JSON部分
      const jsonMatch =
        content.match(/```json\n([\s\S]*?)\n```/) || content.match(/\{[\s\S]*\}/)

      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0]
        const parsed = JSON.parse(jsonStr)

        // 这里可以添加schema验证
        return parsed as T
      }

      return null
    } catch (error) {
      logger.warn('解析结构化输出失败:', error)
      return null
    }
  }

  /**
   * 获取代理配置
   */
  getConfig(): AgentConfig {
    return this.config
  }

  /**
   * 获取代理名称
   */
  getName(): string {
    return this.config.name
  }

  /**
   * 获取代理描述
   */
  getDescription(): string {
    return this.config.description
  }
}
