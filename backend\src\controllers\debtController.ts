import { Request, Response } from 'express'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'

export class DebtController {
  /**
   * 获取用户所有负债
   */
  async getUserDebts(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id

      // 模拟负债数据
      const mockDebts = [
        {
          id: '1',
          userId,
          typeId: 'credit_card',
          name: '招商银行信用卡',
          principalAmount: 50000,
          currentBalance: 12000,
          interestRate: 0.18,
          startDate: '2023-01-15',
          monthlyPayment: 2000,
          currency: 'CNY',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '2',
          userId,
          typeId: 'mortgage',
          name: '房屋抵押贷款',
          principalAmount: 2000000,
          currentBalance: 1800000,
          interestRate: 0.045,
          startDate: '2022-06-01',
          monthlyPayment: 12000,
          currency: 'CNY',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
        {
          id: '3',
          userId,
          typeId: 'auto_loan',
          name: '汽车贷款',
          principalAmount: 300000,
          currentBalance: 150000,
          interestRate: 0.06,
          startDate: '2023-03-01',
          monthlyPayment: 8000,
          currency: 'CNY',
          createdAt: new Date(),
          updatedAt: new Date(),
        },
      ]

      res.json({
        success: true,
        data: mockDebts,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户负债失败:', error)
      res.status(500).json({
        success: false,
        message: '获取负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取单个负债详情
   */
  async getDebtById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { debtId } = req.params

      // 模拟负债详情
      const mockDebt = {
        id: debtId,
        userId,
        typeId: 'credit_card',
        name: '招商银行信用卡',
        principalAmount: 50000,
        currentBalance: 12000,
        interestRate: 0.18,
        startDate: '2023-01-15',
        monthlyPayment: 2000,
        currency: 'CNY',
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      res.json({
        success: true,
        data: mockDebt,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取负债详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取负债详情失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 创建新负债
   */
  async createDebt(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const debtData = req.body

      // 基本验证
      if (
        !debtData.name ||
        !debtData.typeId ||
        debtData.principalAmount === undefined ||
        debtData.currentBalance === undefined ||
        debtData.interestRate === undefined
      ) {
        res.status(400).json({
          success: false,
          message: '负债名称、类型、本金、当前余额和利率为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      // 模拟创建负债
      const newDebt = {
        id: Date.now().toString(),
        userId,
        ...debtData,
        createdAt: new Date(),
        updatedAt: new Date(),
      }

      res.status(201).json({
        success: true,
        data: newDebt,
        message: '负债创建成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('创建负债失败:', error)
      res.status(500).json({
        success: false,
        message: '创建负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 更新负债
   */
  async updateDebt(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { debtId } = req.params
      const updateData = req.body

      // 模拟更新负债
      const updatedDebt = {
        id: debtId,
        userId,
        ...updateData,
        updatedAt: new Date(),
      }

      res.json({
        success: true,
        data: updatedDebt,
        message: '负债更新成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('更新负债失败:', error)
      res.status(500).json({
        success: false,
        message: '更新负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 删除负债
   */
  async deleteDebt(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params

      res.json({
        success: true,
        message: '负债删除成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('删除负债失败:', error)
      res.status(500).json({
        success: false,
        message: '删除负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取负债类型
   */
  async getDebtTypes(req: Request, res: Response): Promise<void> {
    try {
      const debtTypes = [
        {
          id: 'credit_card',
          name: '信用卡',
          description: '信用卡债务',
          icon: 'credit-card',
          color: '#F44336',
        },
        {
          id: 'mortgage',
          name: '房贷',
          description: '房屋抵押贷款',
          icon: 'home',
          color: '#3F51B5',
        },
        {
          id: 'auto_loan',
          name: '车贷',
          description: '汽车贷款',
          icon: 'car',
          color: '#009688',
        },
        {
          id: 'personal_loan',
          name: '个人贷款',
          description: '个人消费贷款',
          icon: 'user',
          color: '#795548',
        },
      ]

      res.json({
        success: true,
        data: debtTypes,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取负债类型失败:', error)
      res.status(500).json({
        success: false,
        message: '获取负债类型失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取负债统计
   */
  async getDebtStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id

      // 模拟负债统计
      const mockStats = {
        totalDebt: 1962000,
        totalMonthlyPayment: 22000,
        averageInterestRate: 0.078,
        debtToIncomeRatio: 0.35,
        typeBreakdown: [
          {
            typeId: 'mortgage',
            typeName: '房贷',
            totalAmount: 1800000,
            percentage: 91.7,
            count: 1,
          },
          {
            typeId: 'auto_loan',
            typeName: '车贷',
            totalAmount: 150000,
            percentage: 7.6,
            count: 1,
          },
          {
            typeId: 'credit_card',
            typeName: '信用卡',
            totalAmount: 12000,
            percentage: 0.6,
            count: 1,
          },
        ],
      }

      res.json({
        success: true,
        data: mockStats,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取负债统计失败:', error)
      res.status(500).json({
        success: false,
        message: '获取负债统计失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 记录还款
   */
  async recordPayment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params
      const { amount, principalAmount, interestAmount, paymentDate } = req.body

      if (!amount || !principalAmount || !interestAmount) {
        res.status(400).json({
          success: false,
          message: '还款金额、本金和利息为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      // 模拟记录还款
      const paymentRecord = {
        id: Date.now().toString(),
        debtId,
        amount,
        principalAmount,
        interestAmount,
        paymentDate: paymentDate || new Date().toISOString().split('T')[0],
        createdAt: new Date(),
      }

      res.status(201).json({
        success: true,
        data: paymentRecord,
        message: '还款记录成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('记录还款失败:', error)
      res.status(500).json({
        success: false,
        message: '记录还款失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取还款历史
   */
  async getPaymentHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params
      const { limit = 20 } = req.query

      // 模拟还款历史
      const mockPayments = []
      const limitNum = parseInt(limit as string, 10)

      for (let i = 0; i < limitNum; i++) {
        const date = new Date()
        date.setMonth(date.getMonth() - i)

        mockPayments.push({
          id: `payment-${i}`,
          debtId,
          amount: 2000 + Math.random() * 500,
          principalAmount: 1500 + Math.random() * 300,
          interestAmount: 500 + Math.random() * 200,
          paymentDate: date.toISOString().split('T')[0],
          createdAt: date,
        })
      }

      res.json({
        success: true,
        data: mockPayments,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取还款历史失败:', error)
      res.status(500).json({
        success: false,
        message: '获取还款历史失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const debtController = new DebtController()
