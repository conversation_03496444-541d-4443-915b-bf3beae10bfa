import { Request, Response } from 'express'
import { logger } from '../utils/logger'
import { debtService } from '../services/debtService'
import type { AuthenticatedRequest } from '../types'

export class DebtController {
  /**
   * 获取用户所有负债
   */
  async getUserDebts(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const debts = await debtService.getAllDebts()

      res.json({
        success: true,
        data: debts,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户负债失败:', error)
      res.status(500).json({
        success: false,
        message: '获取用户负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取单个负债详情
   */
  async getDebtById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params

      const debt = await debtService.getDebtById(debtId)

      if (!debt) {
        res.status(404).json({
          success: false,
          message: '负债不存在',
          timestamp: new Date().toISOString(),
        })
        return
      }

      res.json({
        success: true,
        data: debt,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取负债详情失败:', error)
      res.status(500).json({
        success: false,
        message: '获取负债详情失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 创建新负债
   */
  async createDebt(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const debtData = req.body

      // 基本验证
      if (
        !debtData.name ||
        !debtData.typeId ||
        debtData.principalAmount === undefined ||
        debtData.currentBalance === undefined ||
        debtData.interestRate === undefined ||
        !debtData.startDate
      ) {
        res.status(400).json({
          success: false,
          message: '负债名称、类型、本金、当前余额、利率和开始日期为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const newDebt = await debtService.createDebt(debtData)

      res.status(201).json({
        success: true,
        data: newDebt,
        message: '负债创建成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('创建负债失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '创建负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 更新负债
   */
  async updateDebt(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params
      const updateData = req.body

      const updatedDebt = await debtService.updateDebt(debtId, updateData)

      res.json({
        success: true,
        data: updatedDebt,
        message: '负债更新成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('更新负债失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '更新负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 删除负债
   */
  async deleteDebt(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params

      await debtService.deleteDebt(debtId)

      res.json({
        success: true,
        message: '负债删除成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('删除负债失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '删除负债失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取负债类型
   */
  async getDebtTypes(req: Request, res: Response): Promise<void> {
    try {
      const debtTypes = await debtService.getAllDebtTypes()

      res.json({
        success: true,
        data: debtTypes,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取负债类型失败:', error)
      res.status(500).json({
        success: false,
        message: '获取负债类型失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取负债统计
   */
  async getDebtStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const stats = await debtService.getDebtStatistics()

      res.json({
        success: true,
        data: stats,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取负债统计失败:', error)
      res.status(500).json({
        success: false,
        message: '获取负债统计失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 记录还款
   */
  async recordPayment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params
      const { amount, principalAmount, interestAmount, paymentDate } = req.body

      if (!amount || !principalAmount || !interestAmount) {
        res.status(400).json({
          success: false,
          message: '还款金额、本金和利息为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const paymentRecord = await debtService.addPayment(debtId, {
        amount,
        principalAmount,
        interestAmount,
        paymentDate: paymentDate || new Date().toISOString().split('T')[0],
      })

      res.status(201).json({
        success: true,
        data: paymentRecord,
        message: '还款记录成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('记录还款失败:', error)
      res.status(500).json({
        success: false,
        message: error.message || '记录还款失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取还款历史
   */
  async getPaymentHistory(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { debtId } = req.params

      const payments = await debtService.getDebtPayments(debtId)

      res.json({
        success: true,
        data: payments,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取还款历史失败:', error)
      res.status(500).json({
        success: false,
        message: '获取还款历史失败',
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const debtController = new DebtController()
