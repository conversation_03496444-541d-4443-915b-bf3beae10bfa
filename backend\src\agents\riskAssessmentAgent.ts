import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages'
import { BaseAgent } from './base/BaseAgent'
import { logger } from '../utils/logger'
import type {
  WorkflowState,
  AgentConfig,
  RiskAssessmentResult,
  RiskFactor,
  VolatilityAnalysis,
} from '../types/agents'
import {
  getUserAssets,
  calculatePortfolioRisk,
  getMarketData,
  analyzeAssetAllocation,
} from './tools'

export class RiskAssessmentAgent extends BaseAgent {
  constructor(modelId: string = 'deepseek-chat') {
    const config: AgentConfig = {
      name: 'RiskAssessmentAgent',
      description: '专业的风险评估代理，评估投资风险和资产配置合理性',
      systemPrompt: `你是一位专业的风险管理专家，具有丰富的投资风险评估经验。你的任务是：

1. 全面评估投资组合的风险水平
2. 识别各种风险因素和潜在威胁
3. 分析资产配置的风险分散效果
4. 计算风险指标和波动性分析
5. 提供风险控制和缓解建议

风险评估维度：
- 市场风险：价格波动、市场周期影响
- 信用风险：发行人违约可能性
- 流动性风险：变现能力和时间成本
- 集中度风险：单一资产或行业过度集中
- 通胀风险：购买力下降影响
- 利率风险：利率变化对资产价值影响
- 汇率风险：外币资产的汇率波动
- 操作风险：交易和管理过程中的风险

请基于量化分析和定性判断，提供客观、专业的风险评估报告。`,
      tools: [
        'getUserAssets',
        'calculatePortfolioRisk',
        'getMarketData',
        'analyzeAssetAllocation',
      ],
      maxIterations: 3,
      temperature: 0.2,
      modelId,
    }

    super(config)
  }

  protected initializeTools(): void {
    this.tools.set('getUserAssets', getUserAssets)
    this.tools.set('calculatePortfolioRisk', calculatePortfolioRisk)
    this.tools.set('getMarketData', getMarketData)
    this.tools.set('analyzeAssetAllocation', analyzeAssetAllocation)
  }

  protected async processTask(
    state: WorkflowState,
    messages: BaseMessage[]
  ): Promise<RiskAssessmentResult> {
    try {
      logger.info('开始风险评估分析')

      // 1. 获取用户资产数据
      const assetsResult = await this.callTool('getUserAssets', state.userId)
      if (!assetsResult.success) {
        throw new Error('获取用户资产数据失败')
      }

      const assets = assetsResult.output?.data || []

      // 2. 计算投资组合风险
      const portfolioRiskResult = await this.callTool('calculatePortfolioRisk', assets)
      if (!portfolioRiskResult.success) {
        throw new Error('计算投资组合风险失败')
      }

      const portfolioRisk = portfolioRiskResult.output?.data || {
        overallRisk: 'medium' as const,
        diversificationScore: 0,
        riskFactors: [],
        weightedRisk: 0,
      }

      // 3. 分析资产配置
      const allocationResult = await this.callTool('analyzeAssetAllocation', assets)
      const assetAllocation = allocationResult.success
        ? allocationResult.output?.data || []
        : []

      // 4. 获取市场数据进行波动性分析
      const marketSymbols = this.extractMarketSymbols(assets)
      const marketDataResult = await this.callTool('getMarketData', marketSymbols)
      const marketData = marketDataResult.success
        ? marketDataResult.output?.data || []
        : []

      // 5. 计算波动性指标
      const volatilityAnalysis = this.calculateVolatilityAnalysis(
        assets,
        marketData,
        assetAllocation
      )

      // 6. 识别额外风险因素
      const additionalRiskFactors = this.identifyAdditionalRiskFactors(
        assets,
        assetAllocation,
        state.financialAnalysis,
        state.investmentAdvice
      )

      // 7. 合并所有风险因素
      const allRiskFactors: RiskFactor[] = [
        ...portfolioRisk.riskFactors,
        ...additionalRiskFactors,
      ]

      // 8. 使用AI生成详细风险分析
      const riskPrompt = this.buildRiskPrompt({
        assets,
        portfolioRisk,
        assetAllocation,
        volatilityAnalysis,
        riskFactors: allRiskFactors,
        marketData,
        financialAnalysis: state.financialAnalysis,
        investmentAdvice: state.investmentAdvice,
      })

      const riskMessages = [...messages, new HumanMessage(riskPrompt)]
      const aiResponse = await this.generateResponse(riskMessages)

      // 9. 解析AI建议
      const recommendations = this.extractRiskRecommendations(aiResponse)

      const result: RiskAssessmentResult = {
        overallRisk: portfolioRisk.overallRisk,
        riskFactors: allRiskFactors,
        diversificationScore: portfolioRisk.diversificationScore,
        volatilityAnalysis,
        recommendations,
        timestamp: new Date(),
      }

      logger.info('风险评估分析完成', {
        overallRisk: result.overallRisk,
        riskFactorsCount: result.riskFactors.length,
        diversificationScore: result.diversificationScore,
      })

      return result
    } catch (error) {
      logger.error('风险评估分析失败:', error)
      throw error
    }
  }

  protected async updateState(
    state: WorkflowState,
    result: RiskAssessmentResult
  ): Promise<WorkflowState> {
    return {
      ...state,
      riskAssessment: result,
      currentStep: 'risk_assessment_complete',
      nextSteps: ['news_analysis', 'coordinator'],
      context: {
        ...state.context,
        riskAssessmentComplete: true,
        overallRisk: result.overallRisk,
        diversificationScore: result.diversificationScore,
      },
    }
  }

  private extractMarketSymbols(assets: any[]): string[] {
    const symbols: string[] = []

    assets.forEach((asset) => {
      if (asset.symbol) {
        symbols.push(asset.symbol)
      }
    })

    // 添加基准指数用于波动性比较
    symbols.push('SPY', 'VIX', 'BND', 'GLD')

    return [...new Set(symbols)].slice(0, 10)
  }

  private calculateVolatilityAnalysis(
    assets: any[],
    marketData: any[],
    assetAllocation: any[]
  ): VolatilityAnalysis {
    // 简化的波动性计算，实际应该使用历史价格数据
    const totalValue = assets.reduce((sum, asset) => sum + (asset.currentValue || 0), 0)

    // 基于资产类别估算组合波动性
    let portfolioVolatility = 0
    assetAllocation.forEach((allocation) => {
      const weight = allocation.percentage / 100
      let assetVolatility = 0

      switch (allocation.risk) {
        case 'low':
          assetVolatility = 0.05 // 5%
          break
        case 'medium':
          assetVolatility = 0.15 // 15%
          break
        case 'high':
          assetVolatility = 0.25 // 25%
          break
      }

      portfolioVolatility += weight * assetVolatility
    })

    // 简化的风险指标计算
    const sharpeRatio = portfolioVolatility > 0 ? 0.06 / portfolioVolatility : 0 // 假设无风险利率6%
    const maxDrawdown = portfolioVolatility * 2 // 简化估算
    const valueAtRisk = totalValue * portfolioVolatility * 1.65 // 95%置信度VaR

    return {
      portfolioVolatility,
      sharpeRatio,
      maxDrawdown,
      valueAtRisk,
    }
  }

  private identifyAdditionalRiskFactors(
    assets: any[],
    assetAllocation: any[],
    financialAnalysis?: any,
    investmentAdvice?: any
  ): RiskFactor[] {
    const riskFactors: RiskFactor[] = []

    // 流动性风险评估
    const illiquidAssets = assets.filter(
      (asset) => asset.categoryId === '房地产' || asset.categoryId === '私募股权'
    )
    if (illiquidAssets.length > 0) {
      const illiquidValue = illiquidAssets.reduce(
        (sum, asset) => sum + (asset.currentValue || 0),
        0
      )
      const totalValue = assets.reduce(
        (sum, asset) => sum + (asset.currentValue || 0),
        0
      )
      const illiquidRatio = totalValue > 0 ? illiquidValue / totalValue : 0

      if (illiquidRatio > 0.3) {
        riskFactors.push({
          type: '流动性风险',
          level: 'high',
          description: `非流动性资产占比过高(${(illiquidRatio * 100).toFixed(1)}%)`,
          impact: illiquidRatio,
          mitigation: '增加流动性较好的资产配置，保持适当的现金储备',
        })
      }
    }

    // 货币风险评估
    const foreignAssets = assets.filter(
      (asset) => asset.currency && asset.currency !== 'CNY'
    )
    if (foreignAssets.length > 0) {
      const foreignValue = foreignAssets.reduce(
        (sum, asset) => sum + (asset.currentValue || 0),
        0
      )
      const totalValue = assets.reduce(
        (sum, asset) => sum + (asset.currentValue || 0),
        0
      )
      const foreignRatio = totalValue > 0 ? foreignValue / totalValue : 0

      if (foreignRatio > 0.2) {
        riskFactors.push({
          type: '汇率风险',
          level: 'medium',
          description: `外币资产占比${(foreignRatio * 100).toFixed(
            1
          )}%，存在汇率波动风险`,
          impact: foreignRatio,
          mitigation: '考虑汇率对冲策略，或适当降低外币资产比重',
        })
      }
    }

    // 基于财务分析的风险评估
    if (financialAnalysis) {
      const { financialHealthScore, cashFlow } = financialAnalysis

      if (financialHealthScore < 60) {
        riskFactors.push({
          type: '财务健康风险',
          level: 'high',
          description: `财务健康评分较低(${financialHealthScore}/100)`,
          impact: (100 - financialHealthScore) / 100,
          mitigation: '优先改善财务状况，增加应急资金，降低投资风险',
        })
      }

      if (cashFlow && cashFlow.netCashFlow < 0) {
        riskFactors.push({
          type: '现金流风险',
          level: 'high',
          description: '月度现金流为负，可能需要变现投资',
          impact: Math.abs(cashFlow.netCashFlow) / (cashFlow.monthlyIncome || 1),
          mitigation: '优化支出结构，增加收入来源，保持充足流动性',
        })
      }
    }

    // 基于投资建议的风险评估
    if (investmentAdvice && investmentAdvice.expectedReturn > 0.15) {
      riskFactors.push({
        type: '收益预期风险',
        level: 'medium',
        description: `预期收益率较高(${(investmentAdvice.expectedReturn * 100).toFixed(
          1
        )}%)，伴随较高风险`,
        impact: investmentAdvice.expectedReturn - 0.08, // 超过8%的部分视为额外风险
        mitigation: '合理调整收益预期，做好风险控制和资产配置',
      })
    }

    return riskFactors
  }

  private buildRiskPrompt(data: {
    assets: any[]
    portfolioRisk: any
    assetAllocation: any[]
    volatilityAnalysis: VolatilityAnalysis
    riskFactors: RiskFactor[]
    marketData: any[]
    financialAnalysis?: any
    investmentAdvice?: any
  }): string {
    return `
请基于以下信息进行专业的风险评估分析：

## 投资组合概况
- 总体风险等级：${data.portfolioRisk.overallRisk}
- 多样化评分：${data.portfolioRisk.diversificationScore}/100
- 加权风险值：${data.portfolioRisk.weightedRisk?.toFixed(2)}

## 资产配置分析
${data.assetAllocation
  .map(
    (item) => `- ${item.category}：${item.percentage?.toFixed(1)}% (风险：${item.risk})`
  )
  .join('\n')}

## 波动性分析
- 组合波动率：${(data.volatilityAnalysis.portfolioVolatility * 100).toFixed(2)}%
- 夏普比率：${data.volatilityAnalysis.sharpeRatio.toFixed(2)}
- 最大回撤：${(data.volatilityAnalysis.maxDrawdown * 100).toFixed(2)}%
- 风险价值(VaR)：¥${data.volatilityAnalysis.valueAtRisk.toLocaleString()}

## 识别的风险因素
${data.riskFactors
  .map((risk) => `- ${risk.type} (${risk.level}): ${risk.description}`)
  .join('\n')}

## 市场环境
${data.marketData
  .map((item) => `- ${item.symbol}：${item.changePercent?.toFixed(2)}% (波动性指标)`)
  .join('\n')}

## 财务状况
${
  data.financialAnalysis
    ? `
- 财务健康评分：${data.financialAnalysis.financialHealthScore}/100
- 净现金流：¥${data.financialAnalysis.cashFlow?.netCashFlow?.toLocaleString() || '0'}
`
    : '暂无财务分析数据'
}

## 投资策略
${
  data.investmentAdvice
    ? `
- 风险偏好：${data.investmentAdvice.riskProfile}
- 预期收益：${(data.investmentAdvice.expectedReturn * 100).toFixed(1)}%
`
    : '暂无投资建议数据'
}

请提供：
1. 风险评估总结
2. 主要风险点分析
3. 风险等级判断依据
4. 风险控制建议
5. 应急预案建议
6. 定期监控要点

请确保分析客观准确，建议具体可行。
    `.trim()
  }

  private extractRiskRecommendations(aiResponse: string): string[] {
    const recommendations: string[] = []

    const lines = aiResponse.split('\n')
    let inRecommendations = false

    for (const line of lines) {
      const trimmedLine = line.trim()

      // 检测建议部分的开始
      if (
        trimmedLine.includes('建议') ||
        trimmedLine.includes('控制') ||
        trimmedLine.includes('缓解') ||
        trimmedLine.includes('应对')
      ) {
        inRecommendations = true
        continue
      }

      // 提取列表项
      if (
        inRecommendations &&
        (trimmedLine.startsWith('-') ||
          trimmedLine.startsWith('•') ||
          trimmedLine.match(/^\d+\./))
      ) {
        const recommendation = trimmedLine.replace(/^[-•\d.]\s*/, '').trim()
        if (recommendation.length > 10) {
          recommendations.push(recommendation)
        }
      }

      // 如果遇到新的段落标题，停止提取
      if (inRecommendations && trimmedLine.startsWith('#')) {
        break
      }
    }

    // 如果没有提取到建议，生成默认建议
    if (recommendations.length === 0) {
      recommendations.push(
        '定期监控投资组合风险指标变化',
        '保持适当的资产配置多样化',
        '建立应急资金以应对市场波动',
        '根据市场变化及时调整投资策略',
        '考虑使用风险对冲工具降低波动性'
      )
    }

    return recommendations.slice(0, 10)
  }
}
