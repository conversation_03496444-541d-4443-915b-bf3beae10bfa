import { logger } from '../utils/logger'
import type { User } from '../types'

// 单用户模式的认证服务
export class AuthService {
  private readonly defaultUser: Omit<User, 'password'> = {
    id: 'default-user',
    username: 'user',
    email: 'user@localhost',
    fullName: '用户',

    createdAt: new Date(),
    updatedAt: new Date(),
    lastLoginAt: new Date(),
  }

  /**
   * 获取默认用户信息
   */
  async getCurrentUser(): Promise<Omit<User, 'password'>> {
    return this.defaultUser
  }

  /**
   * 更新用户信息
   */
  async updateUser(updateData: Partial<User>): Promise<Omit<User, 'password'>> {
    // 在单用户模式下，可以将用户信息存储在本地文件或环境变量中
    // 这里简化处理，返回合并后的用户信息
    const updatedUser = {
      ...this.defaultUser,
      ...updateData,
      id: this.defaultUser.id, // 保持ID不变
      updatedAt: new Date(),
    }

    logger.info('用户信息更新成功')
    return updatedUser
  }

  /**
   * 获取用户ID（单用户模式）
   */
  getUserId(): string {
    return this.defaultUser.id
  }

  /**
   * 检查用户是否存在（单用户模式下始终返回true）
   */
  async userExists(): Promise<boolean> {
    return true
  }

  /**
   * 初始化默认用户（如果需要的话）
   */
  async initializeDefaultUser(): Promise<void> {
    logger.info('单用户模式已初始化')
  }
}

export const authService = new AuthService()
