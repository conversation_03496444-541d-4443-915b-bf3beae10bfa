version: '3.8'

services:
  app:
    build: .
    ports:
      - '3000:3000'
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
      - DATABASE_PATH=/app/data/database.sqlite
      - JWT_SECRET=${JWT_SECRET:-your-default-jwt-secret-change-in-production}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-your-default-encryption-key-change-in-production}
    restart: unless-stopped
    healthcheck:
      test: ['CMD', 'curl', '-f', 'http://localhost:3000/api/v1/health']
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  data:
    driver: local
  logs:
    driver: local

networks:
  default:
    name: ai-asset-manager-network
