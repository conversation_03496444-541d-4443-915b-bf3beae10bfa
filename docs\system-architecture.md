# AI 驱动资产管理应用 - 系统架构设计

## 整体架构概览

```mermaid
graph TB
    subgraph "前端层 Frontend Layer"
        UI[React UI Components]
        Store[Zustand State Store]
        Charts[Recharts Visualization]
        Router[React Router]
    end

    subgraph "API网关层 API Gateway"
        Gateway[Express.js API Gateway]
        Auth[JWT Authentication]
        Middleware[Request Middleware]
    end

    subgraph "业务服务层 Business Services"
        AssetService[资产管理服务]
        DebtService[负债管理服务]
        AIService[AI Agent服务]
        NewsService[新闻服务]
        MarketService[市场数据服务]
        CalendarService[日历服务]
    end

    subgraph "AI引擎层 AI Engine"
        LangGraph[Langgraph.js Workflow]
        ModelManager[多模型管理器]
        AdviceEngine[投资建议引擎]
    end

    subgraph "数据访问层 Data Access"
        ORM[Drizzle ORM]
        SQLite[(SQLite Database)]
    end

    subgraph "外部服务 External Services"
        NewsAPI[新闻API MCP Server]
        FinanceAPI[金融数据API]
        AIModels[AI模型APIs]
    end

    subgraph "定时任务 Scheduled Jobs"
        Cron[Node-cron Scheduler]
        DataUpdater[数据更新任务]
    end

    UI --> Gateway
    Store --> Gateway
    Charts --> Store
    Router --> UI

    Gateway --> Auth
    Gateway --> Middleware
    Gateway --> AssetService
    Gateway --> DebtService
    Gateway --> AIService
    Gateway --> NewsService
    Gateway --> MarketService
    Gateway --> CalendarService

    AIService --> LangGraph
    LangGraph --> ModelManager
    LangGraph --> AdviceEngine

    AssetService --> ORM
    DebtService --> ORM
    NewsService --> ORM
    MarketService --> ORM
    CalendarService --> ORM

    ORM --> SQLite

    NewsService --> NewsAPI
    MarketService --> FinanceAPI
    ModelManager --> AIModels

    Cron --> DataUpdater
    DataUpdater --> MarketService
    DataUpdater --> NewsService
```

## 核心模块设计

### 1. 前端架构 (React + TypeScript)

#### 组件层次结构

```mermaid
graph TD
    App[App.tsx]
    App --> Layout[Layout Component]
    App --> Router[Router Configuration]

    Layout --> Header[Header Navigation]
    Layout --> Sidebar[Sidebar Menu]
    Layout --> Main[Main Content Area]

    Main --> Dashboard[Dashboard Page]
    Main --> Assets[Assets Management]
    Main --> Debts[Debts Management]
    Main --> AIChat[AI Chat Interface]
    Main --> News[News Feed]
    Main --> Calendar[Calendar View]

    Assets --> AssetChart[Asset Charts]
    Assets --> AssetForm[Asset Forms]

    Debts --> DebtChart[Debt Charts]
    Debts --> DebtForm[Debt Forms]

    AIChat --> ChatInterface[Chat UI]
    AIChat --> ModelSelector[Model Selector]
```

#### 状态管理结构

- **用户状态**: 认证信息、用户配置、API Keys
- **资产状态**: 资产列表、分类统计、历史数据
- **负债状态**: 负债列表、还款计划、利息计算
- **AI 状态**: 对话历史、模型配置、建议记录
- **市场状态**: 市场数据、新闻列表、更新时间

### 2. 后端架构 (Node.js + TypeScript)

#### 服务层设计

```mermaid
graph LR
    subgraph "Controller Layer"
        AC[Asset Controller]
        DC[Debt Controller]
        AIC[AI Controller]
        NC[News Controller]
        MC[Market Controller]
        CC[Calendar Controller]
    end

    subgraph "Service Layer"
        AS[Asset Service]
        DS[Debt Service]
        AIS[AI Service]
        NS[News Service]
        MS[Market Service]
        CS[Calendar Service]
    end

    subgraph "Repository Layer"
        AR[Asset Repository]
        DR[Debt Repository]
        NR[News Repository]
        MR[Market Repository]
        CR[Calendar Repository]
    end

    AC --> AS
    DC --> DS
    AIC --> AIS
    NC --> NS
    MC --> MS
    CC --> CS

    AS --> AR
    DS --> DR
    AIS --> AR
    NS --> NR
    MS --> MR
    CS --> CR
```

### 3. AI Agent 系统架构

```mermaid
graph TB
    subgraph "AI Agent System"
        Input[用户输入]
        Router[意图路由器]

        subgraph "Agent Workflows"
            AssetAgent[资产分析Agent]
            InvestAgent[投资建议Agent]
            NewsAgent[新闻分析Agent]
            PlanAgent[财务规划Agent]
        end

        subgraph "Tools & Functions"
            DataTool[数据查询工具]
            CalcTool[计算工具]
            ChartTool[图表生成工具]
            NewsTool[新闻检索工具]
        end

        ModelManager[模型管理器]
        Output[结构化输出]
    end

    Input --> Router
    Router --> AssetAgent
    Router --> InvestAgent
    Router --> NewsAgent
    Router --> PlanAgent

    AssetAgent --> DataTool
    InvestAgent --> CalcTool
    NewsAgent --> NewsTool
    PlanAgent --> ChartTool

    AssetAgent --> ModelManager
    InvestAgent --> ModelManager
    NewsAgent --> ModelManager
    PlanAgent --> ModelManager

    ModelManager --> Output
```

## 数据流设计

### 1. 用户交互流程

1. **用户认证** → JWT Token 生成 → 前端状态更新
2. **数据查询** → API 请求 → 服务层处理 → 数据库查询 → 响应返回
3. **AI 对话** → 意图识别 → Agent 选择 → 工具调用 → 模型推理 → 结果返回
4. **数据可视化** → 数据聚合 → 图表渲染 → 交互响应

### 2. 定时任务流程

1. **市场数据更新** → API 调用 → 数据清洗 → 数据库存储 → 缓存更新
2. **新闻抓取** → MCP Server 调用 → 内容筛选 → 分类存储
3. **AI 分析** → 数据分析 → 建议生成 → 用户通知

## 安全架构

### 1. 认证与授权

- JWT Token 认证
- API Key 加密存储
- 用户权限控制
- 请求频率限制

### 2. 数据安全

- 敏感数据加密
- SQL 注入防护
- XSS 攻击防护
- CORS 配置

### 3. AI 安全

- API Key 隔离
- 模型输出过滤
- 用户输入验证
- 调用频率控制

## 性能优化策略

### 1. 前端优化

- 组件懒加载
- 图表数据虚拟化
- 状态缓存策略
- 请求去重和缓存

### 2. 后端优化

- 数据库索引优化
- API 响应缓存
- 批量数据处理
- 连接池管理

### 3. AI 优化

- 模型响应缓存
- 批量推理处理
- 异步任务队列
- 结果预计算
