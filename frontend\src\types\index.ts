// 通用类型定义
export interface User {
  id: string
  email: string
  name: string
  createdAt: string
}

export interface UserSettings {
  id: string
  userId: string
  apiKeys: {
    openai?: string
    anthropic?: string
    deepseek?: string
  }
  preferences: {
    theme: 'light' | 'dark' | 'system'
    language: 'zh-CN' | 'en-US'
    currency: 'CNY' | 'USD' | 'EUR'
  }
}

// 资产相关类型
export interface Asset {
  id: string
  userId: string
  categoryId: string
  name: string
  currentValue: number
  purchasePrice: number
  purchaseDate: string
  createdAt: string
  updatedAt: string
}

export interface AssetCategory {
  id: string
  name: string
  description: string
  icon: string
}

// 负债相关类型
export interface Debt {
  id: string
  userId: string
  typeId: string
  name: string
  principalAmount: number
  currentBalance: number
  interestRate: number
  createdAt: string
  updatedAt: string
}

export interface DebtType {
  id: string
  name: string
  description: string
  icon: string
}

// AI相关类型
export interface AIConversation {
  id: string
  userId: string
  title: string
  createdAt: string
  updatedAt: string
}

export interface AIMessage {
  id: string
  conversationId: string
  role: 'user' | 'assistant'
  content: string
  modelId: string
  tokensUsed?: number
  cost?: number
  createdAt: string
}

export interface AIModel {
  id: string
  provider: 'openai' | 'anthropic' | 'deepseek'
  name: string
  displayName: string
  maxTokens: number
  costPer1kTokens: number
}

// 新闻相关类型
export interface NewsArticle {
  id: string
  title: string
  content: string
  summary: string
  source: string
  url: string
  publishedAt: string
  relevanceScore: number
  category: string
}

// 市场数据类型
export interface MarketData {
  id: string
  indicatorId: string
  value: number
  timestamp: string
}

export interface MarketIndicator {
  id: string
  name: string
  symbol: string
  description: string
  unit: string
}

// API响应类型
export interface ApiResponse<T> {
  success: boolean
  data?: T
  message: string
  timestamp: string
}

export interface ApiError {
  success: false
  error: {
    code: string
    message: string
    details?: any
  }
  timestamp: string
}

// 分页类型
export interface PaginationParams {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}
