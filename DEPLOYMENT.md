# AI 驱动资产管理应用 - 部署指南

本文档提供了 AI 驱动资产管理应用的完整部署指南，包括开发环境和生产环境的部署方法。

## 📋 系统要求

### 基础要求

- **Node.js**: 18.0+ (推荐 22.0+)
- **pnpm**: 8.0+ (包管理器)
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **内存**: 最少 4GB RAM (推荐 8GB+)
- **存储**: 最少 2GB 可用空间

### 可选要求

- **Docker**: 20.0+ (用于容器化部署)
- **Docker Compose**: 2.0+ (用于多容器编排)

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <repository-url>
cd ai-asset-manager
```

### 2. 使用部署脚本

我们提供了自动化部署脚本，支持多种部署模式：

```bash
# 给脚本添加执行权限
chmod +x deploy.sh

# 查看帮助信息
./deploy.sh help

# 部署开发环境
./deploy.sh dev

# 部署生产环境
./deploy.sh prod

# 使用Docker部署
./deploy.sh docker
```

## 🛠️ 手动部署

### 开发环境部署

#### 1. 环境配置

```bash
# 复制环境变量文件
cp backend/.env.example backend/.env
cp frontend/.env.example frontend/.env  # 如果存在

# 编辑后端环境变量
nano backend/.env
```

**后端环境变量配置 (`backend/.env`)**:

```env
# 应用配置
NODE_ENV=development
PORT=3001

# 数据库配置
DATABASE_PATH=../data/database.sqlite

# 认证配置
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
ENCRYPTION_KEY=your-32-character-encryption-key

# AI服务配置（用户自行配置）
# OPENAI_API_KEY=your-openai-api-key
# ANTHROPIC_API_KEY=your-anthropic-api-key
# DEEPSEEK_API_KEY=your-deepseek-api-key

# 外部API配置（可选）
# ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
# NEWS_API_KEY=your-news-api-key
```

#### 2. 安装依赖

```bash
# 安装根目录依赖
pnpm install

# 安装后端依赖
cd backend
pnpm install
cd ..

# 安装前端依赖
cd frontend
pnpm install
cd ..
```

#### 3. 初始化数据库

```bash
cd backend
# 运行数据库迁移（如果有）
pnpm run db:migrate

# 插入种子数据（如果有）
pnpm run db:seed
cd ..
```

#### 4. 启动开发服务

```bash
# 启动后端开发服务
cd backend
pnpm run dev &

# 启动前端开发服务
cd frontend
pnpm run dev &
```

**访问地址**:

- 前端: http://localhost:5173
- 后端 API: http://localhost:3001

### 生产环境部署

#### 方式一：Docker 部署（推荐）

```bash
# 1. 配置环境变量
cp backend/.env.example backend/.env
# 编辑 backend/.env 文件，设置生产环境配置

# 2. 构建并启动容器
docker-compose up -d --build

# 3. 查看服务状态
docker-compose ps

# 4. 查看日志
docker-compose logs -f
```

**访问地址**:

- 前端: http://localhost:3000
- 后端 API: http://localhost:3001

#### 方式二：本地部署

```bash
# 1. 环境配置
cp backend/.env.example backend/.env
# 编辑环境变量，设置 NODE_ENV=production

# 2. 安装依赖
pnpm install
cd backend && pnpm install && cd ..
cd frontend && pnpm install && cd ..

# 3. 构建项目
cd backend && pnpm run build && cd ..
cd frontend && pnpm run build && cd ..

# 4. 初始化数据库
cd backend
pnpm run db:migrate
pnpm run db:seed
cd ..

# 5. 启动生产服务
cd backend
pnpm run start &
cd ..

cd frontend
pnpm run preview &
cd ..
```

## 🐳 Docker 部署详解

### Docker Compose 配置

项目包含完整的 Docker 配置，支持一键部署：

```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - '3000:3000' # 前端
      - '3001:3001' # 后端
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
```

### Docker 命令

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 停止服务
docker-compose down

# 查看日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 更新服务
docker-compose pull
docker-compose up -d
```

## 🔧 配置说明

### 后端配置

**数据库配置**:

- 默认使用 SQLite 数据库
- 数据文件存储在 `data/database.sqlite`
- 支持自动创建表结构和初始数据

**AI 服务配置**:

- 支持多种 AI 提供商（OpenAI、Anthropic、DeepSeek）
- API Key 通过用户设置页面配置
- 支持加密存储 API Key

**安全配置**:

- JWT Token 认证
- API 请求频率限制
- CORS 安全配置

### 前端配置

**API 配置**:

```env
VITE_API_BASE_URL=http://localhost:3001/api/v1
```

**构建配置**:

- 使用 Vite 构建工具
- 支持 TypeScript
- 集成 Tailwind CSS

## 📊 监控和维护

### 健康检查

```bash
# 使用部署脚本检查
./deploy.sh health

# 手动检查后端
curl http://localhost:3001/api/v1/health

# 手动检查前端
curl http://localhost:3000
```

### 日志管理

**查看日志**:

```bash
# Docker环境
docker-compose logs -f

# 本地环境
tail -f logs/app.log
```

**日志轮转**:

- 应用日志自动按日期轮转
- 保留最近 30 天的日志文件
- 错误日志单独记录

### 数据备份

**数据库备份**:

```bash
# 备份SQLite数据库
cp data/database.sqlite backup/database_$(date +%Y%m%d_%H%M%S).sqlite

# 定期备份脚本
#!/bin/bash
BACKUP_DIR="backup"
mkdir -p $BACKUP_DIR
cp data/database.sqlite $BACKUP_DIR/database_$(date +%Y%m%d_%H%M%S).sqlite
find $BACKUP_DIR -name "database_*.sqlite" -mtime +30 -delete
```

## 🔒 安全配置

### 生产环境安全检查清单

- [ ] 更改默认的 JWT_SECRET
- [ ] 更改默认的 ENCRYPTION_KEY
- [ ] 配置 HTTPS（使用反向代理）
- [ ] 设置防火墙规则
- [ ] 定期更新依赖包
- [ ] 配置日志监控
- [ ] 设置自动备份

### HTTPS 配置（Nginx 示例）

```nginx
server {
    listen 443 ssl;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🚨 故障排除

### 常见问题

**1. 端口占用**

```bash
# 查看端口占用
netstat -tulpn | grep :3001
lsof -i :3001

# 杀死占用进程
kill -9 <PID>
```

**2. 数据库连接失败**

```bash
# 检查数据库文件权限
ls -la data/database.sqlite

# 重新初始化数据库
rm data/database.sqlite
cd backend && pnpm run db:migrate && pnpm run db:seed
```

**3. Docker 容器启动失败**

```bash
# 查看详细错误信息
docker-compose logs

# 重新构建镜像
docker-compose build --no-cache

# 清理Docker缓存
docker system prune -a
```

**4. 前端构建失败**

```bash
# 清理node_modules
rm -rf frontend/node_modules
cd frontend && pnpm install

# 清理构建缓存
rm -rf frontend/dist
cd frontend && pnpm run build
```

### 性能优化

**数据库优化**:

- 定期执行 VACUUM 清理
- 监控数据库大小
- 适当添加索引

**应用优化**:

- 启用 gzip 压缩
- 配置静态资源缓存
- 使用 CDN 加速

## 📈 扩展部署

### 负载均衡

使用 Nginx 进行负载均衡：

```nginx
upstream backend {
    server localhost:3001;
    server localhost:3002;
    server localhost:3003;
}

server {
    location /api/ {
        proxy_pass http://backend;
    }
}
```

### 集群部署

```bash
# 启动多个后端实例
PORT=3001 pnpm run start &
PORT=3002 pnpm run start &
PORT=3003 pnpm run start &
```

## 📞 支持

如果在部署过程中遇到问题：

1. 查看本文档的故障排除部分
2. 检查应用日志文件
3. 查看 GitHub Issues
4. 联系技术支持团队

---

**注意**: 在生产环境中，请确保所有安全配置都已正确设置，并定期进行安全更新和备份。
