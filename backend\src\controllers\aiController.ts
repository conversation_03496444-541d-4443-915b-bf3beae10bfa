import { Request, Response } from 'express'
import { aiService } from '../services/aiService'
import { workflowService } from '../services/workflowService'
import { logger } from '../utils/logger'
import type { AuthenticatedRequest } from '../types'

export class AIController {
  /**
   * 创建新对话
   */
  async createConversation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { title, contextType, contextData } = req.body

      if (!title) {
        res.status(400).json({
          success: false,
          message: '对话标题为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const conversation = await aiService.createConversation(
        userId,
        title,
        contextType,
        contextData
      )

      res.status(201).json({
        success: true,
        data: conversation,
        message: '对话创建成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('创建AI对话失败:', error)
      res.status(500).json({
        success: false,
        message: '创建对话失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取用户对话列表
   */
  async getUserConversations(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const conversations = await aiService.getUserConversations(userId)

      res.json({
        success: true,
        data: conversations,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取用户对话失败:', error)
      res.status(500).json({
        success: false,
        message: '获取对话列表失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取对话消息
   */
  async getConversationMessages(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    try {
      const userId = req.user.id
      const { conversationId } = req.params

      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: '对话ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const messages = await aiService.getConversationMessages(userId, conversationId)

      res.json({
        success: true,
        data: messages,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取对话消息失败:', error)

      if (error.message === '对话不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '获取消息失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 发送消息并获取AI回复
   */
  async sendMessage(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { conversationId } = req.params
      const { content, modelId } = req.body

      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: '对话ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      if (!content || !modelId) {
        res.status(400).json({
          success: false,
          message: '消息内容和模型ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const result = await aiService.sendMessage(
        userId,
        conversationId,
        content,
        modelId
      )

      res.json({
        success: true,
        data: result,
        message: '消息发送成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('发送AI消息失败:', error)

      if (error.message === '对话不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else if (error.message.includes('API Key')) {
        res.status(400).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '发送消息失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 流式发送消息
   */
  async sendMessageStream(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { conversationId } = req.params
      const { content, modelId } = req.body

      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: '对话ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      if (!content || !modelId) {
        res.status(400).json({
          success: false,
          message: '消息内容和模型ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const result = await aiService.sendMessageStream(
        userId,
        conversationId,
        content,
        modelId
      )

      // 设置SSE响应头
      res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        Connection: 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
      })

      // 发送用户消息
      res.write(
        `data: ${JSON.stringify({
          type: 'userMessage',
          data: result.userMessage,
        })}\n\n`
      )

      let fullContent = ''

      // 处理流式响应
      for await (const chunk of result.stream) {
        fullContent += chunk
        res.write(
          `data: ${JSON.stringify({
            type: 'chunk',
            data: chunk,
          })}\n\n`
        )
      }

      // 保存完整的AI回复
      const usage = await result.usage
      const aiMessage = await aiService.saveStreamedMessage(
        conversationId,
        modelId,
        fullContent,
        usage?.totalTokens
      )

      // 发送完成信号
      res.write(
        `data: ${JSON.stringify({
          type: 'complete',
          data: aiMessage,
        })}\n\n`
      )

      res.end()
    } catch (error: any) {
      logger.error('流式发送AI消息失败:', error)

      // 发送错误信息
      res.write(
        `data: ${JSON.stringify({
          type: 'error',
          message: error.message,
        })}\n\n`
      )

      res.end()
    }
  }

  /**
   * 获取可用的AI模型
   */
  async getAvailableModels(req: Request, res: Response): Promise<void> {
    try {
      const models = await aiService.getAvailableModels()

      res.json({
        success: true,
        data: models,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取AI模型失败:', error)
      res.status(500).json({
        success: false,
        message: '获取AI模型失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 删除对话
   */
  async deleteConversation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { conversationId } = req.params

      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: '对话ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      await aiService.deleteConversation(userId, conversationId)

      res.json({
        success: true,
        message: '对话删除成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('删除AI对话失败:', error)

      if (error.message === '对话不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '删除对话失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 更新对话标题
   */
  async updateConversationTitle(
    req: AuthenticatedRequest,
    res: Response
  ): Promise<void> {
    try {
      const userId = req.user.id
      const { conversationId } = req.params
      const { title } = req.body

      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: '对话ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      if (!title) {
        res.status(400).json({
          success: false,
          message: '对话标题为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      await aiService.updateConversationTitle(userId, conversationId, title)

      res.json({
        success: true,
        message: '对话标题更新成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('更新对话标题失败:', error)

      if (error.message === '对话不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '更新对话标题失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 获取AI使用统计
   */
  async getAIUsageStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id

      // 这里可以实现AI使用统计的逻辑
      // 比如统计用户的token使用量、成本等

      res.json({
        success: true,
        data: {
          totalConversations: 0,
          totalMessages: 0,
          totalTokensUsed: 0,
          totalCost: 0,
          // 可以添加更多统计信息
        },
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取AI使用统计失败:', error)
      res.status(500).json({
        success: false,
        message: '获取使用统计失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 执行AI代理工作流
   */
  async executeWorkflow(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { conversationId } = req.params
      const { content, modelId, workflowType = 'financial_analysis' } = req.body

      if (!conversationId) {
        res.status(400).json({
          success: false,
          message: '对话ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      if (!content || !modelId) {
        res.status(400).json({
          success: false,
          message: '消息内容和模型ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const result = await aiService.executeWorkflow(
        userId,
        conversationId,
        content,
        modelId,
        workflowType
      )

      res.json({
        success: true,
        data: {
          conversation: result.conversation,
          userMessage: result.userMessage,
          aiMessage: result.aiMessage,
          workflowResult: {
            success: result.workflowResult.success,
            executionTime: result.workflowResult.executionTime,
            stepsCount: result.workflowResult.steps.length,
            currentStep: result.workflowResult.state.currentStep,
            isComplete: result.workflowResult.state.isComplete,
          },
        },
        message: '工作流执行成功',
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('执行AI工作流失败:', error)

      if (error.message === '对话不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else if (error.message.includes('API Key')) {
        res.status(400).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '执行工作流失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 执行单个AI代理
   */
  async executeSingleAgent(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const userId = req.user.id
      const { conversationId, agentName } = req.params
      const { content, modelId } = req.body

      if (!conversationId || !agentName) {
        res.status(400).json({
          success: false,
          message: '对话ID和代理名称为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      if (!content || !modelId) {
        res.status(400).json({
          success: false,
          message: '消息内容和模型ID为必填项',
          timestamp: new Date().toISOString(),
        })
        return
      }

      const result = await aiService.executeSingleAgent(
        userId,
        conversationId,
        content,
        modelId,
        agentName
      )

      res.json({
        success: true,
        data: {
          userMessage: result.userMessage,
          aiMessage: result.aiMessage,
          workflowResult: {
            success: result.workflowResult.success,
            executionTime: result.workflowResult.executionTime,
            agentName,
            currentStep: result.workflowResult.state.currentStep,
          },
        },
        message: `${agentName}代理执行成功`,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error(`执行单个AI代理失败:`, error)

      if (error.message === '对话不存在或无权限') {
        res.status(404).json({
          success: false,
          message: error.message,
          timestamp: new Date().toISOString(),
        })
      } else {
        res.status(500).json({
          success: false,
          message: '执行代理失败',
          timestamp: new Date().toISOString(),
        })
      }
    }
  }

  /**
   * 获取工作流状态
   */
  async getWorkflowStatus(req: Request, res: Response): Promise<void> {
    try {
      const status = await workflowService.getWorkflowStatus()

      res.json({
        success: true,
        data: status,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取工作流状态失败:', error)
      res.status(500).json({
        success: false,
        message: '获取工作流状态失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 获取工作流执行历史
   */
  async getWorkflowHistory(req: Request, res: Response): Promise<void> {
    try {
      const { workflowType = 'financial_analysis' } = req.query
      const history = workflowService.getExecutionHistory(workflowType as string)

      res.json({
        success: true,
        data: {
          workflowType,
          history,
          count: history.length,
        },
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('获取工作流执行历史失败:', error)
      res.status(500).json({
        success: false,
        message: '获取执行历史失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 重置工作流
   */
  async resetWorkflow(req: Request, res: Response): Promise<void> {
    try {
      const { workflowType = 'financial_analysis' } = req.body
      workflowService.resetWorkflow(workflowType)

      res.json({
        success: true,
        message: `工作流 ${workflowType} 已重置`,
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('重置工作流失败:', error)
      res.status(500).json({
        success: false,
        message: '重置工作流失败',
        timestamp: new Date().toISOString(),
      })
    }
  }

  /**
   * 工作流健康检查
   */
  async workflowHealthCheck(req: Request, res: Response): Promise<void> {
    try {
      const isHealthy = await workflowService.healthCheck()

      res.json({
        success: true,
        data: {
          isHealthy,
          status: isHealthy ? 'healthy' : 'unhealthy',
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      })
    } catch (error: any) {
      logger.error('工作流健康检查失败:', error)
      res.status(500).json({
        success: false,
        message: '健康检查失败',
        data: {
          isHealthy: false,
          status: 'error',
        },
        timestamp: new Date().toISOString(),
      })
    }
  }
}

export const aiController = new AIController()
