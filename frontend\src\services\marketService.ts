import { apiClient } from './api'
import type { ApiResponse } from '../types'

export interface MarketIndicator {
  id: string
  name: string
  symbol: string
  description?: string
  category: string
  isActive: boolean
  createdAt: Date
}

export interface MarketData {
  id: string
  indicatorId: string
  value: number
  changeAmount?: number
  changePercentage?: number
  recordDate: string
  createdAt: Date
}

export interface MarketOverview {
  indices: Array<{
    name: string
    symbol: string
    value: number
    change: number
    changePercent: number
    trend: 'up' | 'down' | 'neutral'
  }>
  commodities: Array<{
    name: string
    symbol: string
    value: number
    change: number
    changePercent: number
    trend: 'up' | 'down' | 'neutral'
  }>
  currencies: Array<{
    name: string
    symbol: string
    value: number
    change: number
    changePercent: number
    trend: 'up' | 'down' | 'neutral'
  }>
  marketSentiment: {
    fearGreedIndex: number
    sentiment: 'fear' | 'neutral' | 'greed'
    description: string
  }
  lastUpdated: string
}

export interface MarketAnalysis {
  summary: string
  keyPoints: string[]
  risks: string[]
  opportunities: string[]
  recommendations: string[]
  generatedAt: string
}

class MarketService {
  /**
   * 获取市场指标列表
   */
  async getMarketIndicators(): Promise<ApiResponse<MarketIndicator[]>> {
    return await apiClient.get<MarketIndicator[]>('/market/indicators')
  }

  /**
   * 获取市场概览
   */
  async getMarketOverview(): Promise<ApiResponse<MarketOverview>> {
    return await apiClient.get<MarketOverview>('/market/overview')
  }

  /**
   * 获取市场分析
   */
  async getMarketAnalysis(): Promise<ApiResponse<MarketAnalysis>> {
    return await apiClient.get<MarketAnalysis>('/market/analysis')
  }

  /**
   * 获取指定指标的市场数据
   */
  async getMarketData(
    indicatorId: string,
    days: number = 30
  ): Promise<ApiResponse<MarketData[]>> {
    return await apiClient.get<MarketData[]>(`/market/data/${indicatorId}`, { days })
  }

  /**
   * 获取多个指标的数据
   */
  async getMultipleMarketData(
    indicatorIds: string[],
    days: number = 30
  ): Promise<Record<string, MarketData[]>> {
    const promises = indicatorIds.map((id) => this.getMarketData(id, days))
    const results = await Promise.all(promises)

    const data: Record<string, MarketData[]> = {}
    indicatorIds.forEach((id, index) => {
      if (results[index].success && results[index].data) {
        data[id] = results[index].data!
      }
    })

    return data
  }

  /**
   * 获取热门指标
   */
  async getPopularIndicators(
    limit: number = 10
  ): Promise<ApiResponse<MarketIndicator[]>> {
    const response = await this.getMarketIndicators()
    if (response.success && response.data) {
      // 返回前N个指标作为热门指标
      return {
        ...response,
        data: response.data.slice(0, limit),
      }
    }
    return response
  }

  /**
   * 根据分类获取指标
   */
  async getIndicatorsByCategory(
    category: string
  ): Promise<ApiResponse<MarketIndicator[]>> {
    const response = await this.getMarketIndicators()
    if (response.success && response.data) {
      const filteredIndicators = response.data.filter(
        (indicator) => indicator.category === category
      )
      return {
        ...response,
        data: filteredIndicators,
      }
    }
    return response
  }

  /**
   * 搜索市场指标
   */
  async searchIndicators(query: string): Promise<ApiResponse<MarketIndicator[]>> {
    const response = await this.getMarketIndicators()
    if (response.success && response.data) {
      const filteredIndicators = response.data.filter(
        (indicator) =>
          indicator.name.toLowerCase().includes(query.toLowerCase()) ||
          indicator.symbol.toLowerCase().includes(query.toLowerCase()) ||
          indicator.description?.toLowerCase().includes(query.toLowerCase())
      )
      return {
        ...response,
        data: filteredIndicators,
      }
    }
    return response
  }

  /**
   * 获取指标的历史表现
   */
  async getIndicatorPerformance(
    indicatorId: string,
    period: '1d' | '1w' | '1m' | '3m' | '6m' | '1y' = '1m'
  ): Promise<
    ApiResponse<{
      indicator: MarketIndicator
      data: MarketData[]
      performance: {
        totalReturn: number
        totalReturnPercent: number
        volatility: number
        maxDrawdown: number
        sharpeRatio: number
      }
    }>
  > {
    const daysMap = {
      '1d': 1,
      '1w': 7,
      '1m': 30,
      '3m': 90,
      '6m': 180,
      '1y': 365,
    }

    const days = daysMap[period]
    const [indicatorsResponse, dataResponse] = await Promise.all([
      this.getMarketIndicators(),
      this.getMarketData(indicatorId, days),
    ])

    if (!indicatorsResponse.success || !dataResponse.success) {
      return {
        success: false,
        message: '获取数据失败',
        timestamp: new Date().toISOString(),
      }
    }

    const indicator = indicatorsResponse.data?.find((ind) => ind.id === indicatorId)
    const data = dataResponse.data || []

    if (!indicator) {
      return {
        success: false,
        message: '指标不存在',
        timestamp: new Date().toISOString(),
      }
    }

    // 计算性能指标
    const values = data.map((d) => d.value)
    const returns = values
      .slice(1)
      .map((value, index) => (value - values[index]) / values[index])

    const totalReturn = values.length > 1 ? values[values.length - 1] - values[0] : 0
    const totalReturnPercent = values.length > 1 ? (totalReturn / values[0]) * 100 : 0
    const volatility = this.calculateVolatility(returns)
    const maxDrawdown = this.calculateMaxDrawdown(values)
    const sharpeRatio = this.calculateSharpeRatio(returns, volatility)

    return {
      success: true,
      data: {
        indicator,
        data,
        performance: {
          totalReturn,
          totalReturnPercent,
          volatility,
          maxDrawdown,
          sharpeRatio,
        },
      },
      message: '获取指标表现成功',
      timestamp: new Date().toISOString(),
    }
  }

  /**
   * 计算波动率
   */
  private calculateVolatility(returns: number[]): number {
    if (returns.length === 0) return 0

    const mean = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const variance =
      returns.reduce((sum, ret) => sum + Math.pow(ret - mean, 2), 0) / returns.length
    return Math.sqrt(variance) * Math.sqrt(252) // 年化波动率
  }

  /**
   * 计算最大回撤
   */
  private calculateMaxDrawdown(values: number[]): number {
    if (values.length === 0) return 0

    let maxDrawdown = 0
    let peak = values[0]

    for (const value of values) {
      if (value > peak) {
        peak = value
      }
      const drawdown = (peak - value) / peak
      if (drawdown > maxDrawdown) {
        maxDrawdown = drawdown
      }
    }

    return maxDrawdown * 100 // 转换为百分比
  }

  /**
   * 计算夏普比率
   */
  private calculateSharpeRatio(returns: number[], volatility: number): number {
    if (returns.length === 0 || volatility === 0) return 0

    const meanReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length
    const annualizedReturn = meanReturn * 252 // 年化收益率
    const riskFreeRate = 0.02 // 假设无风险利率为2%

    return (annualizedReturn - riskFreeRate) / volatility
  }

  /**
   * 获取市场热力图数据
   */
  async getMarketHeatmap(): Promise<
    ApiResponse<
      Array<{
        name: string
        symbol: string
        value: number
        change: number
        changePercent: number
        marketCap?: number
        sector?: string
      }>
    >
  > {
    const overview = await this.getMarketOverview()
    if (overview.success && overview.data) {
      const heatmapData = [
        ...overview.data.indices.map((item) => ({
          ...item,
          sector: 'Index',
        })),
        ...overview.data.commodities.map((item) => ({
          ...item,
          sector: 'Commodity',
        })),
        ...overview.data.currencies.map((item) => ({
          ...item,
          sector: 'Currency',
        })),
      ]

      return {
        success: true,
        data: heatmapData,
        message: '获取热力图数据成功',
        timestamp: new Date().toISOString(),
      }
    }

    return {
      success: false,
      message: '获取热力图数据失败',
      timestamp: new Date().toISOString(),
    }
  }
}

export const marketService = new MarketService()
