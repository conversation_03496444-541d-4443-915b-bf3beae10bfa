import React, { useState, useEffect } from 'react'
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  List,
  Avatar,
  Tag,
  Spin,
  message,
} from 'antd'
import {
  WalletOutlined,
  CreditCardOutlined,
  RiseOutlined,
  DollarOutlined,
  <PERSON>UpOutlined,
  <PERSON>DownOutlined,
  SwapOutlined,
  B<PERSON>bOutlined,
} from '@ant-design/icons'
import {
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts'
import { assetService } from '../../services/assetService'
import { debtService, type Debt, type DebtStatistics } from '../../services/debtService'
import { aiService } from '../../services/aiService'
import type { Asset } from '../../types'

const { Title, Text } = Typography

interface DashboardStats {
  totalAssets: number
  totalLiabilities: number
  netWorth: number
  monthlyReturn: number
}

interface AssetTrendData {
  date: string
  value: number
  assets: number
  liabilities: number
}

interface AssetDistribution {
  name: string
  value: number
  color: string
}

interface RecentTransaction {
  id: string
  type: 'buy' | 'sell' | 'dividend'
  asset: string
  amount: number
  date: string
  price?: number
}

interface AIRecommendation {
  id: string
  type: 'buy' | 'sell' | 'hold'
  asset: string
  reason: string
  confidence: number
  date: string
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

export const Dashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    totalAssets: 0,
    totalLiabilities: 0,
    netWorth: 0,
    monthlyReturn: 0,
  })
  const [assets, setAssets] = useState<Asset[]>([])
  const [debts, setDebts] = useState<Debt[]>([])
  const [debtStats, setDebtStats] = useState<DebtStatistics | null>(null)
  const [trendData, setTrendData] = useState<AssetTrendData[]>([])
  const [distributionData, setDistributionData] = useState<AssetDistribution[]>([])
  const [recentTransactions, setRecentTransactions] = useState<RecentTransaction[]>([])
  const [aiRecommendations, setAIRecommendations] = useState<AIRecommendation[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)

      // 并行加载资产和负债数据
      const [assetsResponse, debtsResponse, debtStatsResponse] = await Promise.all([
        assetService.getAssets(),
        debtService.getDebts(),
        debtService.getDebtStatistics(),
      ])

      let assetsData: Asset[] = []
      let debtsData: Debt[] = []

      if (assetsResponse.success && assetsResponse.data) {
        assetsData = assetsResponse.data
        setAssets(assetsData)
      }

      if (debtsResponse.success && debtsResponse.data) {
        debtsData = debtsResponse.data
        setDebts(debtsData)
      }

      if (debtStatsResponse.success && debtStatsResponse.data) {
        setDebtStats(debtStatsResponse.data)
      }

      // 计算统计数据
      calculateStats(assetsData, debtsData)
      generateTrendData(assetsData, debtsData)
      generateDistributionData(assetsData)
      generateRecentTransactions(assetsData)

      // 生成AI建议
      await generateAIRecommendations()
    } catch (error) {
      message.error('加载仪表板数据失败')
    } finally {
      setLoading(false)
    }
  }

  const calculateStats = (assets: Asset[], debts: Debt[]) => {
    const totalAssets = assets.reduce((sum, asset) => {
      return sum + asset.currentValue
    }, 0)

    // 使用真实负债数据
    const totalLiabilities = debts.reduce((sum, debt) => {
      return sum + debt.currentBalance
    }, 0)

    const netWorth = totalAssets - totalLiabilities

    // 计算月收益率
    const totalCost = assets.reduce((sum, asset) => sum + asset.purchasePrice, 0)
    const monthlyReturn =
      totalCost > 0 ? ((totalAssets - totalCost) / totalCost) * 100 : 0

    setStats({
      totalAssets,
      totalLiabilities,
      netWorth,
      monthlyReturn,
    })
  }

  const generateTrendData = (assets: Asset[], debts: Debt[]) => {
    // 生成过去30天的趋势数据
    const data: AssetTrendData[] = []
    const today = new Date()

    const currentAssetValue = assets.reduce((sum, asset) => sum + asset.currentValue, 0)
    const currentDebtValue = debts.reduce((sum, debt) => sum + debt.currentBalance, 0)

    for (let i = 29; i >= 0; i--) {
      const date = new Date(today)
      date.setDate(date.getDate() - i)

      // 模拟数据变化
      const assetVariation = (Math.random() - 0.5) * 0.05 // ±2.5% 变化
      const debtVariation = (Math.random() - 0.5) * 0.02 // ±1% 变化

      const dayAssetValue = currentAssetValue * (1 + assetVariation * (i / 30))
      const dayDebtValue = currentDebtValue * (1 + debtVariation * (i / 30))

      data.push({
        date: date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' }),
        value: Math.round(dayAssetValue - dayDebtValue),
        assets: Math.round(dayAssetValue),
        liabilities: Math.round(dayDebtValue),
      })
    }

    setTrendData(data)
  }

  const generateDistributionData = (assets: Asset[]) => {
    const typeMap = new Map<string, number>()

    assets.forEach((asset) => {
      const current = typeMap.get(asset.categoryId) || 0
      const value = asset.currentValue
      typeMap.set(asset.categoryId, current + value)
    })

    const data: AssetDistribution[] = Array.from(typeMap.entries()).map(
      ([name, value], index) => ({
        name: getAssetTypeName(name),
        value: Math.round(value),
        color: COLORS[index % COLORS.length],
      })
    )

    setDistributionData(data)
  }

  const generateRecentTransactions = (assets: Asset[]) => {
    const transactions: RecentTransaction[] = []

    // 基于资产生成模拟交易记录
    assets.slice(0, 5).forEach((asset, index) => {
      const date = new Date()
      date.setDate(date.getDate() - index - 1)

      transactions.push({
        id: `tx_${index}`,
        type: Math.random() > 0.5 ? 'buy' : 'sell',
        asset: asset.name,
        amount: Math.round(asset.currentValue * 0.1),
        date: date.toLocaleDateString('zh-CN'),
        price: asset.purchasePrice,
      })
    })

    setRecentTransactions(transactions)
  }

  const generateAIRecommendations = async () => {
    // 模拟AI建议
    const recommendations: AIRecommendation[] = [
      {
        id: 'ai_1',
        type: 'buy',
        asset: '科技股ETF',
        reason: '科技板块估值合理，未来增长潜力较大',
        confidence: 85,
        date: new Date().toLocaleDateString('zh-CN'),
      },
      {
        id: 'ai_2',
        type: 'hold',
        asset: '债券基金',
        reason: '当前利率环境下，债券提供稳定收益',
        confidence: 78,
        date: new Date().toLocaleDateString('zh-CN'),
      },
      {
        id: 'ai_3',
        type: 'sell',
        asset: '房地产股票',
        reason: '房地产行业面临政策压力，建议减仓',
        confidence: 72,
        date: new Date().toLocaleDateString('zh-CN'),
      },
    ]

    setAIRecommendations(recommendations)
  }

  const getAssetTypeName = (type: string): string => {
    const typeNames: Record<string, string> = {
      stock: '股票',
      bond: '债券',
      fund: '基金',
      crypto: '数字货币',
      real_estate: '房地产',
      commodity: '商品',
      cash: '现金',
    }
    return typeNames[type] || type
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'buy':
        return <ArrowUpOutlined style={{ color: '#52c41a' }} />
      case 'sell':
        return <ArrowDownOutlined style={{ color: '#ff4d4f' }} />
      default:
        return <SwapOutlined style={{ color: '#1890ff' }} />
    }
  }

  const getRecommendationColor = (type: string) => {
    switch (type) {
      case 'buy':
        return 'success'
      case 'sell':
        return 'error'
      case 'hold':
        return 'warning'
      default:
        return 'default'
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Spin size="large" />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <Title level={2} className="mb-6">
          仪表板
        </Title>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总资产"
              value={stats.totalAssets}
              precision={2}
              valueStyle={{ color: '#3f8600' }}
              prefix={<WalletOutlined />}
              suffix="¥"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总负债"
              value={stats.totalLiabilities}
              precision={2}
              valueStyle={{ color: '#cf1322' }}
              prefix={<CreditCardOutlined />}
              suffix="¥"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="净资产"
              value={stats.netWorth}
              precision={2}
              valueStyle={{ color: '#1890ff' }}
              prefix={<DollarOutlined />}
              suffix="¥"
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="月收益率"
              value={stats.monthlyReturn}
              precision={2}
              valueStyle={{ color: stats.monthlyReturn >= 0 ? '#3f8600' : '#cf1322' }}
              prefix={<RiseOutlined />}
              suffix="%"
            />
          </Card>
        </Col>
      </Row>

      {/* 图表区域 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <Card title="资产趋势" className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={trendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip
                  formatter={(value: number) => [`¥${value.toLocaleString()}`, '']}
                  labelFormatter={(label) => `日期: ${label}`}
                />
                <Legend />
                <Area
                  type="monotone"
                  dataKey="assets"
                  stackId="1"
                  stroke="#8884d8"
                  fill="#8884d8"
                  name="总资产"
                />
                <Area
                  type="monotone"
                  dataKey="liabilities"
                  stackId="1"
                  stroke="#82ca9d"
                  fill="#82ca9d"
                  name="总负债"
                />
              </AreaChart>
            </ResponsiveContainer>
          </Card>
        </Col>
        <Col xs={24} lg={8}>
          <Card title="资产分布" className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={distributionData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) =>
                    `${name} ${(percent * 100).toFixed(0)}%`
                  }
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {distributionData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip
                  formatter={(value: number) => [`¥${value.toLocaleString()}`, '价值']}
                />
              </PieChart>
            </ResponsiveContainer>
          </Card>
        </Col>
      </Row>

      {/* 最新动态 */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title="最新交易" className="h-80">
            <List
              dataSource={recentTransactions}
              renderItem={(transaction) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={getTransactionIcon(transaction.type)} />}
                    title={
                      <div className="flex justify-between items-center">
                        <span>{transaction.asset}</span>
                        <Tag color={transaction.type === 'buy' ? 'green' : 'red'}>
                          {transaction.type === 'buy' ? '买入' : '卖出'}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div>数量: {transaction.amount}</div>
                        <div>价格: ¥{transaction.price?.toFixed(2)}</div>
                        <div>日期: {transaction.date}</div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="AI 建议" className="h-80">
            <List
              dataSource={aiRecommendations}
              renderItem={(recommendation) => (
                <List.Item>
                  <List.Item.Meta
                    avatar={
                      <Avatar
                        icon={<BulbOutlined />}
                        style={{ backgroundColor: '#1890ff' }}
                      />
                    }
                    title={
                      <div className="flex justify-between items-center">
                        <span>{recommendation.asset}</span>
                        <Tag color={getRecommendationColor(recommendation.type)}>
                          {recommendation.type === 'buy'
                            ? '买入'
                            : recommendation.type === 'sell'
                            ? '卖出'
                            : '持有'}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div className="mb-1">{recommendation.reason}</div>
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>置信度: {recommendation.confidence}%</span>
                          <span>{recommendation.date}</span>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>
    </div>
  )
}
