# AI 驱动资产管理应用

一个现代化的 AI 驱动资产管理和理财投资建议网页应用，帮助用户管理个人财务、跟踪投资组合并获得智能投资建议。

## ✨ 主要功能

- 📊 **资产管理**: 全面跟踪现金、股票、债券、房产、车辆等各类资产
- 💳 **负债管理**: 管理信用卡、房贷、车贷等债务，制定还款计划
- 🤖 **AI 投资建议**: 基于多种 AI 模型的智能投资分析和建议
- 📰 **新闻集成**: 自动获取和筛选相关金融新闻
- 📈 **市场数据**: 实时市场指标和历史数据分析
- 📅 **日历提醒**: 财务事件和重要日期管理
- 📤 **数据导出**: 支持 SQLite、JSON、CSV 格式数据导出
- 👤 **单用户模式**: 专为个人自部署设计，无需注册登录

## 🏗️ 技术架构

### 前端技术栈

- **React 18** + TypeScript
- **Zustand** 状态管理
- **Ant Design** UI 组件库
- **Recharts** 数据可视化
- **Tailwind CSS** 样式框架
- **Vite** 构建工具

### 后端技术栈

- **Node.js 22** + TypeScript
- **Express.js** Web 框架
- **SQLite3** + Drizzle ORM
- **Vercel AI SDK** AI 集成
- **JWT** 认证
- **better-sqlite3** 数据库驱动

### AI 能力

- 支持多种 AI 模型（OpenAI GPT-4、Anthropic Claude、DeepSeek 等）
- 用户自定义 API Key 管理
- 流式响应体验
- 智能投资建议和风险分析

## 🚀 快速开始

### 环境要求

- Node.js 22+
- pnpm 8+
- Docker（可选）

### 方式一：Docker 部署（推荐）

```bash
# 1. 克隆项目
git clone <repository-url>
cd ai-asset-manager

# 2. 配置环境变量
cp .env.example .env
# 编辑.env文件，配置必要的API Keys

# 3. 启动服务
docker-compose up -d

# 4. 访问应用
# 前端: http://localhost:3000
# 后端API: http://localhost:3001
```

### 方式二：本地开发

```bash
# 1. 克隆项目
git clone <repository-url>
cd ai-asset-manager

# 2. 安装依赖
pnpm install

# 3. 配置环境变量
cp backend/.env.example backend/.env
# 编辑backend/.env文件

# 4. 初始化数据库
pnpm run db:migrate
pnpm run db:seed

# 5. 启动开发服务器
pnpm run dev
```

## 📁 项目结构

```
ai-asset-manager/
├── frontend/                 # React前端应用
│   ├── src/
│   │   ├── components/      # UI组件
│   │   ├── pages/          # 页面组件
│   │   ├── stores/         # 状态管理
│   │   ├── services/       # API服务
│   │   └── types/          # 类型定义
│   └── package.json
├── backend/                  # Node.js后端服务
│   ├── src/
│   │   ├── controllers/    # 控制器
│   │   ├── services/       # 业务服务
│   │   ├── routes/         # 路由定义
│   │   ├── middleware/     # 中间件
│   │   ├── database/       # 数据库相关
│   │   └── ai/            # AI服务
│   └── package.json
├── data/                     # SQLite数据库文件
├── logs/                     # 日志文件
├── docker-compose.yml        # Docker编排
├── Dockerfile               # Docker构建
└── package.json             # 根项目配置
```

## 🔧 配置说明

### 环境变量配置

在 `backend/.env` 文件中配置以下变量：

```env
# 应用配置
NODE_ENV=development
PORT=3001

# 数据库配置
DATABASE_PATH=../data/database.sqlite

# 数据加密配置
ENCRYPTION_KEY=your-encryption-key

# AI服务配置（用户自行配置）
OPENAI_API_KEY=your-openai-api-key
ANTHROPIC_API_KEY=your-anthropic-api-key
DEEPSEEK_API_KEY=your-deepseek-api-key

# 外部API配置（可选）
ALPHA_VANTAGE_API_KEY=your-alpha-vantage-key
NEWS_API_KEY=your-news-api-key
```

## 📖 使用指南

### 1. 首次访问

- 访问应用首页，系统会自动初始化单用户模式
- 无需注册或登录，直接开始使用

### 2. 配置 AI 服务

- 进入设置页面
- 添加您的 AI API Keys（OpenAI、Anthropic、DeepSeek 等）
- 选择默认 AI 模型

### 3. 添加资产和负债

- 在资产管理页面添加您的各类资产
- 在负债管理页面记录债务信息
- 系统会自动计算净资产和相关统计

### 4. 获取 AI 建议

- 在 AI 洞察页面与 AI 助手对话
- 获取基于您财务状况的个性化投资建议
- 查看风险分析和资产配置建议

### 5. 数据更新

- 手动触发市场数据更新
- 获取最新的金融新闻
- 查看市场趋势和分析

### 6. 数据导出

- 导出 SQLite 数据库文件进行备份
- 导出 JSON 或 CSV 格式的数据用于分析

## 🛠️ 开发指南

### 开发命令

```bash
# 启动开发环境
pnpm run dev

# 构建项目
pnpm run build

# 运行测试
pnpm run test

# 代码检查
pnpm run lint

# 数据库操作
pnpm run db:migrate    # 运行迁移
pnpm run db:seed       # 插入种子数据
```

### 添加新功能

1. **前端组件**: 在 `frontend/src/components/` 中创建新组件
2. **API 端点**: 在 `backend/src/routes/` 中添加新路由
3. **业务逻辑**: 在 `backend/src/services/` 中实现业务逻辑
4. **数据库**: 更新 `backend/src/database/schema.ts` 中的数据模型

## 🐳 Docker 部署

### 构建和运行

```bash
# 构建镜像
docker-compose build

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境部署

1. 设置环境变量
2. 配置反向代理（Nginx）
3. 设置 SSL 证书
4. 配置自动备份

## 🔒 安全特性

- API Key 加密存储
- 请求频率限制
- CORS 安全配置
- 输入验证和 SQL 注入防护
- 敏感数据加密
- 单用户模式安全设计

## 📊 监控和维护

- 健康检查端点：`/api/v1/health`
- 应用日志记录
- 错误追踪和报告
- 性能监控指标

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📚 详细文档

更多详细的技术文档请查看 [`docs/`](docs/) 目录：

- [架构设计文档](docs/architecture-design-document.md) - 完整的系统架构设计
- [API 接口规范](docs/api-specification.md) - RESTful API 接口文档
- [数据库设计](docs/database-design.md) - 数据库结构和关系设计
- [项目结构说明](docs/project-structure.md) - 详细的项目文件结构
- [技术挑战分析](docs/technical-challenges.md) - 技术难点和解决方案

## 🆘 支持和反馈

如果您遇到问题或有建议，请：

1. 查看 [FAQ](docs/FAQ.md)
2. 搜索现有的 [Issues](../../issues)
3. 创建新的 Issue 描述问题
4. 联系开发团队

## 🎯 路线图

- [ ] 移动端 PWA 支持
- [ ] 银行账户 API 集成
- [ ] 高级投资分析算法
- [ ] 多用户协作功能
- [ ] 云同步选项
- [ ] 更多 AI 模型支持

---

**注意**: 这是一个个人财务管理工具，请妥善保管您的 API Keys 和敏感数据。建议在生产环境中使用强密码和定期备份数据。
