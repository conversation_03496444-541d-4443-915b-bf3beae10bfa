import { db } from '../../database/connection'
import { assets, debts, userSettings } from '../../database/schema'
import { eq } from 'drizzle-orm'
import { logger } from '../../utils/logger'
import axios from 'axios'
import type { RiskFactor, InvestmentRecommendation } from '../../types/agents'

/**
 * 获取用户资产数据
 */
export async function getUserAssets(userId: string) {
  try {
    const userAssets = await db().select().from(assets).where(eq(assets.userId, userId))

    return {
      success: true,
      data: userAssets,
    }
  } catch (error) {
    logger.error('获取用户资产失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取资产失败',
    }
  }
}

/**
 * 获取用户债务数据
 */
export async function getUserDebts(userId: string) {
  try {
    const userDebts = await db().select().from(debts).where(eq(debts.userId, userId))

    return {
      success: true,
      data: userDebts,
    }
  } catch (error) {
    logger.error('获取用户债务失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取债务失败',
    }
  }
}

/**
 * 获取用户设置和偏好
 */
export async function getUserProfile(userId: string) {
  try {
    const settings = await db()
      .select()
      .from(userSettings)
      .where(eq(userSettings.userId, userId))
      .limit(1)

    return {
      success: true,
      data: settings[0] || null,
    }
  } catch (error) {
    logger.error('获取用户设置失败:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '获取用户设置失败',
    }
  }
}

/**
 * 计算资产总值
 */
export function calculateTotalAssets(assets: any[]) {
  try {
    const total = assets.reduce((sum, asset) => {
      return sum + (asset.currentValue || 0)
    }, 0)

    return {
      success: true,
      data: total,
    }
  } catch (error) {
    return {
      success: false,
      error: '计算资产总值失败',
    }
  }
}

/**
 * 计算债务总额
 */
export function calculateTotalDebts(debts: any[]) {
  try {
    const total = debts.reduce((sum, debt) => {
      return sum + (debt.currentBalance || 0)
    }, 0)

    return {
      success: true,
      data: total,
    }
  } catch (error) {
    return {
      success: false,
      error: '计算债务总额失败',
    }
  }
}

/**
 * 计算净资产
 */
export function calculateNetWorth(totalAssets: number, totalDebts: number) {
  try {
    const netWorth = totalAssets - totalDebts

    return {
      success: true,
      data: netWorth,
    }
  } catch (error) {
    return {
      success: false,
      error: '计算净资产失败',
    }
  }
}

/**
 * 分析资产配置
 */
export function analyzeAssetAllocation(assets: any[]) {
  try {
    const categoryMap = new Map<string, number>()
    const totalValue = assets.reduce((sum, asset) => sum + (asset.currentValue || 0), 0)

    // 按类别分组
    assets.forEach((asset) => {
      const category = asset.categoryId || '其他'
      const currentValue = categoryMap.get(category) || 0
      categoryMap.set(category, currentValue + (asset.currentValue || 0))
    })

    // 计算百分比
    const allocation = Array.from(categoryMap.entries()).map(([category, value]) => ({
      category,
      value,
      percentage: totalValue > 0 ? (value / totalValue) * 100 : 0,
      risk: determineRiskLevel(category),
    }))

    return {
      success: true,
      data: allocation,
    }
  } catch (error) {
    return {
      success: false,
      error: '分析资产配置失败',
    }
  }
}

/**
 * 确定风险等级
 */
function determineRiskLevel(category: string): 'low' | 'medium' | 'high' {
  const lowRiskCategories = ['现金', '储蓄', '债券', '定期存款']
  const highRiskCategories = ['股票', '加密货币', '期货', '期权']

  if (lowRiskCategories.some((cat) => category.includes(cat))) {
    return 'low'
  } else if (highRiskCategories.some((cat) => category.includes(cat))) {
    return 'high'
  } else {
    return 'medium'
  }
}

/**
 * 计算财务健康评分
 */
export function calculateFinancialHealthScore(
  netWorth: number,
  totalAssets: number,
  totalDebts: number,
  monthlyIncome: number = 0
) {
  try {
    let score = 0

    // 净资产评分 (40%)
    if (netWorth > 0) {
      score += 40
      if (netWorth > monthlyIncome * 6) score += 10 // 有6个月以上的应急资金
    }

    // 债务比率评分 (30%)
    const debtRatio = totalAssets > 0 ? totalDebts / totalAssets : 1
    if (debtRatio < 0.3) score += 30
    else if (debtRatio < 0.5) score += 20
    else if (debtRatio < 0.7) score += 10

    // 资产多样化评分 (20%)
    // 这里简化处理，实际应该分析资产配置的多样性
    if (totalAssets > 0) score += 20

    // 流动性评分 (10%)
    // 简化处理，假设有一定的流动性
    score += 10

    return {
      success: true,
      data: Math.min(100, Math.max(0, score)),
    }
  } catch (error) {
    return {
      success: false,
      error: '计算财务健康评分失败',
    }
  }
}

/**
 * 获取市场数据 (模拟)
 */
export async function getMarketData(symbols: string[] = []) {
  try {
    // 这里应该调用真实的市场数据API
    // 现在返回模拟数据
    const mockData = symbols.map((symbol) => ({
      symbol,
      price: Math.random() * 1000 + 100,
      change: (Math.random() - 0.5) * 20,
      changePercent: (Math.random() - 0.5) * 10,
      volume: Math.floor(Math.random() * 1000000),
      timestamp: new Date(),
    }))

    return {
      success: true,
      data: mockData,
    }
  } catch (error) {
    return {
      success: false,
      error: '获取市场数据失败',
    }
  }
}

/**
 * 获取新闻数据 (模拟)
 */
export async function getFinancialNews(keywords: string[] = [], limit: number = 10) {
  try {
    // 这里应该调用真实的新闻API
    // 现在返回模拟数据
    const mockNews = Array.from({ length: limit }, (_, i) => ({
      id: `news_${i}`,
      title: `财经新闻标题 ${i + 1}`,
      summary: `这是一条关于${keywords.join('、')}的财经新闻摘要...`,
      source: '财经新闻网',
      publishedAt: new Date(Date.now() - i * 3600000), // 每小时一条
      relevanceScore: Math.random(),
      sentimentScore: (Math.random() - 0.5) * 2, // -1 到 1
      categories: ['财经', '投资'],
      url: `https://example.com/news/${i}`,
    }))

    return {
      success: true,
      data: mockNews,
    }
  } catch (error) {
    return {
      success: false,
      error: '获取新闻数据失败',
    }
  }
}

/**
 * 计算投资组合风险
 */
export function calculatePortfolioRisk(assets: any[]) {
  try {
    if (assets.length === 0) {
      return {
        success: true,
        data: {
          overallRisk: 'low' as const,
          diversificationScore: 0,
          riskFactors: [],
        },
      }
    }

    const totalValue = assets.reduce((sum, asset) => sum + (asset.currentValue || 0), 0)
    let weightedRisk = 0
    const riskFactors: RiskFactor[] = []

    // 计算加权风险
    assets.forEach((asset) => {
      const weight = (asset.currentValue || 0) / totalValue
      const riskLevel = determineRiskLevel(asset.categoryId || '其他')
      const riskScore = riskLevel === 'low' ? 1 : riskLevel === 'medium' ? 2 : 3
      weightedRisk += weight * riskScore

      if (weight > 0.3) {
        // 单一资产占比超过30%
        riskFactors.push({
          type: '集中度风险',
          level: 'high' as const,
          description: `${asset.name}占投资组合比重过高(${(weight * 100).toFixed(1)}%)`,
          impact: weight,
          mitigation: '建议分散投资，降低单一资产占比',
        })
      }
    })

    // 计算多样化评分
    const categories = new Set(assets.map((asset) => asset.categoryId))
    const diversificationScore = Math.min(100, (categories.size / 5) * 100) // 假设5个类别为满分

    const overallRisk =
      weightedRisk < 1.5 ? 'low' : weightedRisk < 2.5 ? 'medium' : 'high'

    return {
      success: true,
      data: {
        overallRisk,
        diversificationScore,
        riskFactors,
        weightedRisk,
      },
    }
  } catch (error) {
    return {
      success: false,
      error: '计算投资组合风险失败',
    }
  }
}

/**
 * 生成投资建议
 */
export function generateInvestmentRecommendations(
  assets: any[],
  riskProfile: 'conservative' | 'moderate' | 'aggressive',
  targetAllocation?: Record<string, number>
) {
  try {
    const recommendations: InvestmentRecommendation[] = []
    const currentAllocation = analyzeAssetAllocation(assets)

    if (!currentAllocation.success || !currentAllocation.data) {
      throw new Error('无法分析当前资产配置')
    }

    // 基于风险偏好的建议配置
    const targetAllocations: Record<string, Record<string, number>> = {
      conservative: { 现金: 20, 债券: 50, 股票: 20, 其他: 10 },
      moderate: { 现金: 10, 债券: 30, 股票: 50, 其他: 10 },
      aggressive: { 现金: 5, 债券: 15, 股票: 70, 其他: 10 },
    }

    const target = targetAllocation || targetAllocations[riskProfile]
    const current = currentAllocation.data

    // 生成再平衡建议
    if (target) {
      Object.entries(target).forEach(([category, targetPercent]) => {
        const currentItem = current.find((item) => item.category === category)
        const currentPercent = currentItem ? currentItem.percentage : 0
        const difference = (targetPercent as number) - currentPercent

        if (Math.abs(difference) > 5) {
          // 差异超过5%才建议调整
          recommendations.push({
            action: difference > 0 ? 'buy' : 'sell',
            asset: category,
            percentage: Math.abs(difference),
            reason: `当前${category}配置为${currentPercent.toFixed(
              1
            )}%，建议调整至${targetPercent}%`,
            priority:
              Math.abs(difference) > 15
                ? 'high'
                : Math.abs(difference) > 10
                ? 'medium'
                : 'low',
          } as InvestmentRecommendation)
        }
      })
    }

    return {
      success: true,
      data: recommendations,
    }
  } catch (error) {
    return {
      success: false,
      error: '生成投资建议失败',
    }
  }
}

// 导出所有工具函数
export const tools = {
  getUserAssets,
  getUserDebts,
  getUserProfile,
  calculateTotalAssets,
  calculateTotalDebts,
  calculateNetWorth,
  analyzeAssetAllocation,
  calculateFinancialHealthScore,
  getMarketData,
  getFinancialNews,
  calculatePortfolioRisk,
  generateInvestmentRecommendations,
}
