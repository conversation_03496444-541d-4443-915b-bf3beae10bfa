import React, { useState, useEffect } from 'react'
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Button,
  Table,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  DatePicker,
  message,
  Popconfirm,
  Progress,
  Tooltip,
} from 'antd'
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  CreditCardOutlined,
  DollarOutlined,
  CalendarOutlined,
  PercentageOutlined,
  InfoCircleOutlined,
} from '@ant-design/icons'
import {
  debtService,
  type Debt,
  type DebtType,
  type DebtStatistics,
} from '../../services/debtService'
import dayjs from 'dayjs'

const { Title, Text } = Typography
const { Option } = Select

export const LiabilitiesPage: React.FC = () => {
  const [debts, setDebts] = useState<Debt[]>([])
  const [debtTypes, setDebtTypes] = useState<DebtType[]>([])
  const [statistics, setStatistics] = useState<DebtStatistics | null>(null)
  const [loading, setLoading] = useState(true)
  const [modalVisible, setModalVisible] = useState(false)
  const [editingDebt, setEditingDebt] = useState<Debt | null>(null)
  const [form] = Form.useForm()

  useEffect(() => {
    loadData()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)
      const [debtsRes, typesRes, statsRes] = await Promise.all([
        debtService.getDebts(),
        debtService.getDebtTypes(),
        debtService.getDebtStatistics(),
      ])

      if (debtsRes.success) setDebts(debtsRes.data || [])
      if (typesRes.success) setDebtTypes(typesRes.data || [])
      if (statsRes.success) setStatistics(statsRes.data || null)
    } catch (error) {
      message.error('加载数据失败')
    } finally {
      setLoading(false)
    }
  }

  const handleCreate = () => {
    setEditingDebt(null)
    form.resetFields()
    setModalVisible(true)
  }

  const handleEdit = (debt: Debt) => {
    setEditingDebt(debt)
    form.setFieldsValue({
      ...debt,
      startDate: dayjs(debt.startDate),
    })
    setModalVisible(true)
  }

  const handleDelete = async (id: string) => {
    try {
      const response = await debtService.deleteDebt(id)
      if (response.success) {
        message.success('删除成功')
        loadData()
      }
    } catch (error) {
      message.error('删除失败')
    }
  }

  const handleSubmit = async (values: any) => {
    try {
      const debtData = {
        ...values,
        startDate: values.startDate.format('YYYY-MM-DD'),
        currency: values.currency || 'CNY',
      }

      let response
      if (editingDebt) {
        response = await debtService.updateDebt(editingDebt.id, debtData)
      } else {
        response = await debtService.createDebt(debtData)
      }

      if (response.success) {
        message.success(editingDebt ? '更新成功' : '创建成功')
        setModalVisible(false)
        loadData()
      }
    } catch (error) {
      message.error(editingDebt ? '更新失败' : '创建失败')
    }
  }

  const getTypeInfo = (typeId: string) => {
    return debtTypes.find((type) => type.id === typeId)
  }

  const formatCurrency = (amount: number, currency: string = 'CNY') => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: currency === 'CNY' ? 'CNY' : 'USD',
    }).format(amount)
  }

  const calculateProgress = (debt: Debt) => {
    const paid = debt.principalAmount - debt.currentBalance
    return (paid / debt.principalAmount) * 100
  }

  const columns = [
    {
      title: '负债名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: Debt) => {
        const typeInfo = getTypeInfo(record.typeId)
        return (
          <Space>
            <Tag color={typeInfo?.color}>{typeInfo?.name}</Tag>
            <Text strong>{text}</Text>
          </Space>
        )
      },
    },
    {
      title: '当前余额',
      dataIndex: 'currentBalance',
      key: 'currentBalance',
      render: (amount: number, record: Debt) => (
        <Text strong style={{ color: '#cf1322' }}>
          {formatCurrency(amount, record.currency)}
        </Text>
      ),
      sorter: (a: Debt, b: Debt) => a.currentBalance - b.currentBalance,
    },
    {
      title: '本金',
      dataIndex: 'principalAmount',
      key: 'principalAmount',
      render: (amount: number, record: Debt) => formatCurrency(amount, record.currency),
    },
    {
      title: '利率',
      dataIndex: 'interestRate',
      key: 'interestRate',
      render: (rate: number) => `${(rate * 100).toFixed(2)}%`,
      sorter: (a: Debt, b: Debt) => a.interestRate - b.interestRate,
    },
    {
      title: '月还款',
      dataIndex: 'monthlyPayment',
      key: 'monthlyPayment',
      render: (amount: number | undefined, record: Debt) =>
        amount ? formatCurrency(amount, record.currency) : '-',
    },
    {
      title: '还款进度',
      key: 'progress',
      render: (_: any, record: Debt) => {
        const progress = calculateProgress(record)
        return (
          <Progress
            percent={progress}
            size="small"
            status={progress > 80 ? 'success' : progress > 50 ? 'active' : 'normal'}
          />
        )
      },
    },
    {
      title: '开始日期',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: '操作',
      key: 'actions',
      render: (_: any, record: Debt) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个负债吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger icon={<DeleteOutlined />}>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <Title level={2} className="mb-0">
          负债管理
        </Title>
        <Button type="primary" icon={<PlusOutlined />} onClick={handleCreate}>
          添加负债
        </Button>
      </div>

      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="负债总数"
                value={statistics.totalDebts}
                prefix={<CreditCardOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="总余额"
                value={statistics.totalBalance}
                precision={2}
                prefix={<DollarOutlined />}
                suffix="¥"
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title="月还款总额"
                value={statistics.totalMonthlyPayment}
                precision={2}
                prefix={<CalendarOutlined />}
                suffix="¥"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} lg={6}>
            <Card>
              <Statistic
                title={
                  <Space>
                    负债健康度
                    <Tooltip title="基于负债总额、利率、多样性等因素计算">
                      <InfoCircleOutlined />
                    </Tooltip>
                  </Space>
                }
                value={debtService.calculateDebtHealthScore(debts).score}
                suffix="/100"
                valueStyle={{
                  color:
                    debtService.calculateDebtHealthScore(debts).score >= 80
                      ? '#52c41a'
                      : debtService.calculateDebtHealthScore(debts).score >= 60
                      ? '#faad14'
                      : '#f5222d',
                }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 负债列表 */}
      <Card title="负债列表">
        <Table
          columns={columns}
          dataSource={debts}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`,
          }}
        />
      </Card>

      {/* 添加/编辑负债模态框 */}
      <Modal
        title={editingDebt ? '编辑负债' : '添加负债'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="负债名称"
                rules={[{ required: true, message: '请输入负债名称' }]}
              >
                <Input placeholder="如：招商银行信用卡" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="typeId"
                label="负债类型"
                rules={[{ required: true, message: '请选择负债类型' }]}
              >
                <Select placeholder="选择负债类型">
                  {debtTypes.map((type) => (
                    <Option key={type.id} value={type.id}>
                      <Space>
                        <Tag color={type.color}>{type.name}</Tag>
                        {type.description}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="principalAmount"
                label="本金金额"
                rules={[{ required: true, message: '请输入本金金额' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  min={0}
                  precision={2}
                  formatter={(value) =>
                    `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="currentBalance"
                label="当前余额"
                rules={[{ required: true, message: '请输入当前余额' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  min={0}
                  precision={2}
                  formatter={(value) =>
                    `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="interestRate"
                label="年利率"
                rules={[{ required: true, message: '请输入年利率' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0.05"
                  min={0}
                  max={1}
                  step={0.001}
                  precision={4}
                  formatter={(value) => `${(Number(value) * 100).toFixed(2)}%`}
                  parser={(value) => (Number(value!.replace('%', '')) / 100).toString()}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="monthlyPayment" label="月还款额（可选）">
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="0.00"
                  min={0}
                  precision={2}
                  formatter={(value) =>
                    `¥ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                  }
                  parser={(value) => value!.replace(/¥\s?|(,*)/g, '')}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="startDate"
                label="开始日期"
                rules={[{ required: true, message: '请选择开始日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="currency" label="货币" initialValue="CNY">
                <Select>
                  <Option value="CNY">人民币 (CNY)</Option>
                  <Option value="USD">美元 (USD)</Option>
                  <Option value="EUR">欧元 (EUR)</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  )
}
