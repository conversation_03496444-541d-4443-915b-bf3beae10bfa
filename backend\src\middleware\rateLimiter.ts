import { Request, Response, NextFunction } from 'express'

interface RateLimitStore {
  [key: string]: {
    count: number
    resetTime: number
  }
}

class RateLimiter {
  private store: RateLimitStore = {}
  private windowMs: number
  private maxRequests: number

  constructor(windowMs: number = 15 * 60 * 1000, maxRequests: number = 100) {
    this.windowMs = windowMs
    this.maxRequests = maxRequests

    // 定期清理过期记录
    setInterval(() => {
      this.cleanup()
    }, this.windowMs)
  }

  private cleanup(): void {
    const now = Date.now()
    Object.keys(this.store).forEach((key) => {
      if (this.store[key]!.resetTime < now) {
        delete this.store[key]
      }
    })
  }

  private getKey(req: Request): string {
    // 使用IP地址作为限流键
    return req.ip || req.connection.remoteAddress || 'unknown'
  }

  middleware() {
    return (req: Request, res: Response, next: NextFunction): void => {
      const key = this.getKey(req)
      const now = Date.now()

      // 获取或创建记录
      if (!this.store[key] || this.store[key]!.resetTime < now) {
        this.store[key] = {
          count: 1,
          resetTime: now + this.windowMs,
        }
      } else {
        this.store[key]!.count++
      }

      const record = this.store[key]!

      // 设置响应头
      res.set({
        'X-RateLimit-Limit': this.maxRequests.toString(),
        'X-RateLimit-Remaining': Math.max(
          0,
          this.maxRequests - record.count
        ).toString(),
        'X-RateLimit-Reset': new Date(record.resetTime).toISOString(),
      })

      // 检查是否超过限制
      if (record.count > this.maxRequests) {
        res.status(429).json({
          success: false,
          error: {
            code: 'RATE_LIMIT_EXCEEDED',
            message: '请求频率过高，请稍后再试',
          },
          timestamp: new Date().toISOString(),
        })
        return
      }

      next()
    }
  }
}

// 创建默认限流器实例
const defaultRateLimiter = new RateLimiter(
  parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15分钟
  parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100') // 100次请求
)

export const rateLimiter = defaultRateLimiter.middleware()

// 创建严格限流器（用于敏感操作）
const strictRateLimiter = new RateLimiter(
  5 * 60 * 1000, // 5分钟
  10 // 10次请求
)

export const strictRateLimit = strictRateLimiter.middleware()

// 创建AI请求限流器
const aiRateLimiter = new RateLimiter(
  60 * 1000, // 1分钟
  20 // 20次请求
)

export const aiRateLimit = aiRateLimiter.middleware()
