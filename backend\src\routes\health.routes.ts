import { Router, Request, Response } from 'express'
import { asyncHand<PERSON> } from '@/middleware/errorHandler'

const router: Router = Router()

// 健康检查端点
router.get(
  '/',
  asyncHandler(async (req: Request, res: Response) => {
    const healthCheck = {
      uptime: process.uptime(),
      message: 'OK',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
    }

    res.status(200).json({
      success: true,
      data: healthCheck,
      message: '服务运行正常',
      timestamp: new Date().toISOString(),
    })
  })
)

// 详细健康检查端点
router.get(
  '/detailed',
  asyncHandler(async (req: Request, res: Response) => {
    const memoryUsage = process.memoryUsage()

    const detailedHealth = {
      uptime: process.uptime(),
      message: 'OK',
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'development',
      version: '1.0.0',
      memory: {
        rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
        heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
        heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
        external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`,
      },
      cpu: {
        loadAverage:
          process.platform !== 'win32' ? require('os').loadavg() : 'N/A (Windows)',
      },
      database: {
        status: 'connected', // 这里可以添加实际的数据库连接检查
      },
    }

    res.status(200).json({
      success: true,
      data: detailedHealth,
      message: '详细健康检查完成',
      timestamp: new Date().toISOString(),
    })
  })
)

export default router
